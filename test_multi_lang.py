#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多语种ASR服务测试脚本
测试多语种模式下的语种切换功能
"""

import asyncio
import json
import websockets
import base64
import numpy as np
import torch
import torchaudio
from typing import Dict, Any

class MultiLangASRTester:
    """多语种ASR测试器"""
    
    def __init__(self, server_url: str = "ws://localhost:8080/ws"):
        self.server_url = server_url
        self.websocket = None
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect(self.server_url)
            print(f"已连接到服务器: {self.server_url}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print("已断开连接")
    
    def generate_test_audio(self, duration: float = 0.4, sample_rate: int = 16000, frequency: float = 440.0) -> bytes:
        """
        生成测试音频数据
        Args:
            duration: 音频时长（秒）
            sample_rate: 采样率
            frequency: 音频频率
        Returns:
            bytes: PCM音频数据
        """
        # 生成正弦波
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave = np.sin(2 * np.pi * frequency * t)
        
        # 转换为16位PCM
        wave_int16 = (wave * 32767).astype(np.int16)
        return wave_int16.tobytes()
    
    def generate_silence(self, duration: float = 0.4, sample_rate: int = 16000) -> bytes:
        """
        生成静音数据
        Args:
            duration: 静音时长（秒）
            sample_rate: 采样率
        Returns:
            bytes: 静音PCM数据
        """
        samples = int(sample_rate * duration)
        silence = np.zeros(samples, dtype=np.int16)
        return silence.tobytes()
    
    async def send_audio_packet(self, audio_data: bytes, index: int, is_final: bool = False, 
                               enable_lid: bool = True, custom_separator: str = None) -> Dict[str, Any]:
        """
        发送音频数据包
        Args:
            audio_data: 音频数据
            index: 数据包索引
            is_final: 是否为最后一个包
            enable_lid: 是否启用LID
            custom_separator: 自定义分隔符
        Returns:
            Dict: 服务器响应
        """
        # 构造数据包
        packet = {
            "index": index,
            "is_final": is_final,
            "audio_data": base64.b64encode(audio_data).decode('utf-8'),
            "sample_rate": 16000
        }
        
        # 第一个包添加配置信息
        if index == 0:
            packet["enable_lid"] = enable_lid
            if custom_separator:
                packet["custom_separator"] = custom_separator
        
        # 发送数据包
        await self.websocket.send(json.dumps(packet))
        
        # 接收响应
        response = await self.websocket.recv()
        return json.loads(response)
    
    async def test_multi_language_conversation(self):
        """测试多语种对话场景"""
        print("\n=== 测试多语种对话场景 ===")
        
        if not await self.connect():
            return
        
        try:
            # 模拟第一个说话人（中文）
            print("\n1. 模拟中文语音片段...")
            for i in range(6):  # 2.4秒的中文语音
                audio_data = self.generate_test_audio(frequency=440.0 + i * 10)
                response = await self.send_audio_packet(audio_data, i, is_final=False)
                print(f"  包{i}: {response.get('message', 'OK')}")
                if 'language' in response:
                    print(f"    检测语种: {response['language']}")
            
            # 模拟静音片段
            print("\n2. 模拟静音片段...")
            for i in range(12):  # 4.8秒静音（超过静音阈值）
                silence_data = self.generate_silence()
                response = await self.send_audio_packet(silence_data, 6 + i, is_final=False)
                print(f"  静音包{i}: {response.get('message', 'OK')}")
            
            # 模拟第二个说话人（英文）
            print("\n3. 模拟英文语音片段...")
            for i in range(6):  # 2.4秒的英文语音
                audio_data = self.generate_test_audio(frequency=880.0 + i * 20)  # 不同频率模拟不同语种
                response = await self.send_audio_packet(audio_data, 18 + i, is_final=False)
                print(f"  包{i}: {response.get('message', 'OK')}")
                if 'language' in response:
                    print(f"    检测语种: {response['language']}")
            
            # 发送最后一个包
            final_response = await self.send_audio_packet(b'', 24, is_final=True)
            print(f"\n最终响应: {final_response}")
            
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
        finally:
            await self.disconnect()
    
    async def test_single_language_mode(self):
        """测试单语种模式兼容性"""
        print("\n=== 测试单语种模式兼容性 ===")
        
        if not await self.connect():
            return
        
        try:
            # 发送几个音频包
            for i in range(3):
                audio_data = self.generate_test_audio()
                response = await self.send_audio_packet(audio_data, i, is_final=(i==2), enable_lid=False)
                print(f"包{i}: {response.get('message', 'OK')}")
                
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
        finally:
            await self.disconnect()

async def main():
    """主测试函数"""
    print("多语种ASR服务测试")
    print("请确保服务器已启动: python server.py multi 8080")
    
    tester = MultiLangASRTester("ws://localhost:8080/ws")
    
    # 测试多语种对话
    await tester.test_multi_language_conversation()
    
    # 等待一段时间
    await asyncio.sleep(2)
    
    # 测试单语种兼容性
    await tester.test_single_language_mode()

if __name__ == "__main__":
    asyncio.run(main())
