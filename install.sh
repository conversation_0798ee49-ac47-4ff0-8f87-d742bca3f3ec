#!/bin/bash

# ASR服务器安装脚本
# 用于快速部署ASR服务器环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_system() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持Linux系统"
        exit 1
    fi
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    log_info "检测到Python版本: $PYTHON_VERSION"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3未安装，请先安装pip3"
        exit 1
    fi
    
    # 检查内存
    TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    log_info "系统内存: ${TOTAL_MEM}GB"
    
    if [ "$TOTAL_MEM" -lt 4 ]; then
        log_warn "系统内存少于4GB，可能影响性能"
    fi
    
    log_info "系统环境检查通过"
}

# 安装系统依赖
install_system_deps() {
    log_step "安装系统依赖..."
    
    # 检测包管理器
    if command -v apt-get &> /dev/null; then
        PKG_MANAGER="apt-get"
        UPDATE_CMD="apt-get update"
        INSTALL_CMD="apt-get install -y"
        PACKAGES="gcc g++ make libsndfile1-dev python3-dev python3-venv curl"
    elif command -v yum &> /dev/null; then
        PKG_MANAGER="yum"
        UPDATE_CMD="yum update -y"
        INSTALL_CMD="yum install -y"
        PACKAGES="gcc gcc-c++ make libsndfile-devel python3-devel python3-venv curl"
    else
        log_error "不支持的包管理器，请手动安装依赖"
        exit 1
    fi
    
    log_info "使用包管理器: $PKG_MANAGER"
    
    # 更新包列表
    sudo $UPDATE_CMD
    
    # 安装依赖包
    sudo $INSTALL_CMD $PACKAGES
    
    log_info "系统依赖安装完成"
}

# 创建虚拟环境
create_venv() {
    log_step "创建Python虚拟环境..."
    
    if [ -d "venv" ]; then
        log_warn "虚拟环境已存在，跳过创建"
    else
        python3 -m venv venv
        log_info "虚拟环境创建完成"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    log_info "虚拟环境准备完成"
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖包..."
    
    # 确保虚拟环境已激活
    source venv/bin/activate
    
    # 安装基础依赖
    pip install -r requirements.txt
    
    # 尝试安装psutil（可选）
    log_info "尝试安装psutil（用于更好的系统监控）..."
    if pip install psutil; then
        log_info "psutil安装成功"
    else
        log_warn "psutil安装失败，将使用fallback监控方法"
    fi
    
    log_info "Python依赖安装完成"
}

# 创建必要目录
create_directories() {
    log_step "创建必要目录..."
    
    mkdir -p logs
    mkdir -p models
    mkdir -p backup
    
    # 设置权限
    chmod 755 logs models backup
    
    log_info "目录创建完成"
}

# 配置文件检查
check_config() {
    log_step "检查配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        log_warn "config.yaml不存在，请确保配置文件正确"
    else
        log_info "找到主配置文件: config.yaml"
    fi
    
    # 检查语种配置文件
    for lang in zh en ru kk ug; do
        config_file="config_${lang}.yaml"
        if [ -f "$config_file" ]; then
            log_info "找到语种配置文件: $config_file"
        else
            log_warn "未找到语种配置文件: $config_file"
        fi
    done
}

# 创建启动脚本
create_start_script() {
    log_step "创建启动脚本..."
    
    cat > start_server.sh << 'EOF'
#!/bin/bash

# ASR服务器启动脚本

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH=$(pwd)

# 启动参数
HOST=${HOST:-"0.0.0.0"}
PORT=${PORT:-8000}
CONFIG=${CONFIG:-"config.yaml"}
LOG_LEVEL=${LOG_LEVEL:-"INFO"}

# 启动服务
if [ "$1" = "multi-lang" ]; then
    echo "启动多语种模式..."
    python server.py --multi-lang --host $HOST --port $PORT --config $CONFIG --log-level $LOG_LEVEL
else
    LANG=${LANG:-"zh"}
    echo "启动单语种模式: $LANG"
    python server.py --lang $LANG --host $HOST --port $PORT --config config_${LANG}.yaml --log-level $LOG_LEVEL
fi
EOF
    
    chmod +x start_server.sh
    
    log_info "启动脚本创建完成: start_server.sh"
}

# 创建systemd服务文件
create_systemd_service() {
    log_step "创建systemd服务文件..."
    
    SERVICE_FILE="asr-server.service"
    CURRENT_DIR=$(pwd)
    CURRENT_USER=$(whoami)
    
    cat > $SERVICE_FILE << EOF
[Unit]
Description=ASR Server
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
Group=$CURRENT_USER
WorkingDirectory=$CURRENT_DIR
Environment=PYTHONPATH=$CURRENT_DIR
ExecStart=$CURRENT_DIR/venv/bin/python server.py --multi-lang --config config.yaml
Restart=always
RestartSec=10

# 资源限制
LimitNOFILE=65536
MemoryMax=8G

[Install]
WantedBy=multi-user.target
EOF
    
    log_info "systemd服务文件创建完成: $SERVICE_FILE"
    log_info "要安装系统服务，请运行:"
    log_info "  sudo cp $SERVICE_FILE /etc/systemd/system/"
    log_info "  sudo systemctl daemon-reload"
    log_info "  sudo systemctl enable asr-server"
    log_info "  sudo systemctl start asr-server"
}

# 创建备份脚本
create_backup_script() {
    log_step "创建备份脚本..."
    
    cat > backup.sh << 'EOF'
#!/bin/bash

# ASR服务器备份脚本

BACKUP_DIR="backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="asr-server-backup-$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR/$BACKUP_NAME

# 备份配置文件
cp -r config*.yaml $BACKUP_DIR/$BACKUP_NAME/ 2>/dev/null || true

# 备份日志文件
if [ -d "logs" ]; then
    cp -r logs/ $BACKUP_DIR/$BACKUP_NAME/
fi

# 压缩备份
tar -czf $BACKUP_DIR/$BACKUP_NAME.tar.gz -C $BACKUP_DIR $BACKUP_NAME

# 清理临时目录
rm -rf $BACKUP_DIR/$BACKUP_NAME

echo "备份完成: $BACKUP_DIR/$BACKUP_NAME.tar.gz"

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "asr-server-backup-*.tar.gz" -mtime +7 -delete
EOF
    
    chmod +x backup.sh
    
    log_info "备份脚本创建完成: backup.sh"
}

# 运行测试
run_tests() {
    log_step "运行基础测试..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 测试导入
    python3 -c "
import sys
try:
    import fastapi
    import uvicorn
    import onnxruntime
    import numpy
    print('✓ 核心依赖导入成功')
except ImportError as e:
    print(f'✗ 依赖导入失败: {e}')
    sys.exit(1)

try:
    import psutil
    print('✓ psutil可用，将使用完整监控功能')
except ImportError:
    print('⚠ psutil不可用，将使用fallback监控方法')

print('✓ 基础测试通过')
"
    
    log_info "基础测试完成"
}

# 显示安装完成信息
show_completion_info() {
    log_step "安装完成！"
    
    echo ""
    echo "=========================================="
    echo "ASR服务器安装完成"
    echo "=========================================="
    echo ""
    echo "启动服务:"
    echo "  多语种模式: ./start_server.sh multi-lang"
    echo "  单语种模式: ./start_server.sh"
    echo ""
    echo "或者直接使用Python:"
    echo "  source venv/bin/activate"
    echo "  python server.py --multi-lang --config config.yaml"
    echo ""
    echo "服务地址:"
    echo "  主服务: http://localhost:8000"
    echo "  监控服务: http://localhost:8081"
    echo "  WebSocket: ws://localhost:8000/ws/{client_id}"
    echo ""
    echo "重要文件:"
    echo "  配置文件: config.yaml, config_*.yaml"
    echo "  日志目录: logs/"
    echo "  启动脚本: start_server.sh"
    echo "  备份脚本: backup.sh"
    echo ""
    echo "下一步:"
    echo "  1. 配置模型文件路径"
    echo "  2. 调整配置参数"
    echo "  3. 启动服务进行测试"
    echo ""
    echo "详细使用说明请参考: USER_MANUAL.md"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "ASR服务器自动安装脚本"
    echo "=========================================="
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "server.py" ] || [ ! -f "requirements.txt" ]; then
        log_error "请在ASR服务器项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_system
    install_system_deps
    create_venv
    install_python_deps
    create_directories
    check_config
    create_start_script
    create_systemd_service
    create_backup_script
    run_tests
    show_completion_info
}

# 运行主函数
main "$@"
