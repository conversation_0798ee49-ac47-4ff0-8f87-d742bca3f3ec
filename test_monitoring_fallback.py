#!/usr/bin/env python3
"""
测试监控模块的fallback功能
在没有psutil的情况下测试系统监控功能
"""

import os
import sys
import time

def get_memory_info_fallback():
    """
    在没有psutil的情况下获取内存信息（Linux系统）
    """
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = f.read()
        
        lines = meminfo.split('\n')
        mem_total = 0
        mem_available = 0
        
        for line in lines:
            if line.startswith('MemTotal:'):
                mem_total = int(line.split()[1]) * 1024  # 转换为字节
            elif line.startswith('MemAvailable:'):
                mem_available = int(line.split()[1]) * 1024  # 转换为字节
        
        if mem_total > 0:
            mem_used = mem_total - mem_available
            mem_percent = (mem_used / mem_total) * 100
            return {
                'total': mem_total,
                'used': mem_used,
                'percent': mem_percent
            }
    except Exception as e:
        print(f"获取内存信息失败: {e}")
    
    return {'total': 0, 'used': 0, 'percent': 0}


def get_cpu_info_fallback():
    """
    在没有psutil的情况下获取CPU信息（Linux系统）
    """
    try:
        with open('/proc/loadavg', 'r') as f:
            load_avg = f.read().strip().split()
        
        # 使用1分钟平均负载作为CPU使用率的近似值
        load_1min = float(load_avg[0])
        
        # 获取CPU核心数
        cpu_count = os.cpu_count() or 1
        
        # 将负载转换为百分比（简化计算）
        cpu_percent = min((load_1min / cpu_count) * 100, 100)
        
        return cpu_percent
    except Exception as e:
        print(f"获取CPU信息失败: {e}")
        return 0


def get_disk_info_fallback(path='/'):
    """
    在没有psutil的情况下获取磁盘信息（Linux系统）
    """
    try:
        statvfs = os.statvfs(path)
        total = statvfs.f_frsize * statvfs.f_blocks
        free = statvfs.f_frsize * statvfs.f_available
        used = total - free
        percent = (used / total) * 100 if total > 0 else 0
        
        return {
            'total': total,
            'used': used,
            'free': free,
            'percent': percent
        }
    except Exception as e:
        print(f"获取磁盘信息失败: {e}")
        return {'total': 0, 'used': 0, 'free': 0, 'percent': 0}


def format_bytes(bytes_value):
    """格式化字节数为可读格式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.2f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.2f} PB"


def test_fallback_monitoring():
    """测试fallback监控功能"""
    print("=" * 50)
    print("测试ASR服务器监控fallback功能")
    print("=" * 50)
    print()
    
    # 检查操作系统
    if os.name != 'posix':
        print("⚠️  警告: fallback监控功能仅支持Linux系统")
        print(f"当前系统: {os.name}")
        return
    
    print("🔍 检查系统信息...")
    print(f"操作系统: {os.name}")
    print(f"CPU核心数: {os.cpu_count()}")
    print()
    
    # 测试内存信息
    print("📊 测试内存信息获取...")
    mem_info = get_memory_info_fallback()
    if mem_info['total'] > 0:
        print(f"✅ 内存总量: {format_bytes(mem_info['total'])}")
        print(f"✅ 内存已用: {format_bytes(mem_info['used'])}")
        print(f"✅ 内存使用率: {mem_info['percent']:.2f}%")
    else:
        print("❌ 内存信息获取失败")
    print()
    
    # 测试CPU信息
    print("🖥️  测试CPU信息获取...")
    cpu_percent = get_cpu_info_fallback()
    if cpu_percent >= 0:
        print(f"✅ CPU负载: {cpu_percent:.2f}%")
    else:
        print("❌ CPU信息获取失败")
    print()
    
    # 测试磁盘信息
    print("💾 测试磁盘信息获取...")
    disk_info = get_disk_info_fallback()
    if disk_info['total'] > 0:
        print(f"✅ 磁盘总量: {format_bytes(disk_info['total'])}")
        print(f"✅ 磁盘已用: {format_bytes(disk_info['used'])}")
        print(f"✅ 磁盘可用: {format_bytes(disk_info['free'])}")
        print(f"✅ 磁盘使用率: {disk_info['percent']:.2f}%")
    else:
        print("❌ 磁盘信息获取失败")
    print()
    
    # 测试psutil可用性
    print("🔧 测试psutil可用性...")
    try:
        import psutil
        print("✅ psutil可用，建议使用psutil获得更准确的监控数据")
        
        # 比较psutil和fallback结果
        print("\n📈 psutil vs fallback 对比:")
        
        # 内存对比
        psutil_mem = psutil.virtual_memory()
        print(f"内存使用率 - psutil: {psutil_mem.percent:.2f}%, fallback: {mem_info['percent']:.2f}%")
        
        # CPU对比
        psutil_cpu = psutil.cpu_percent(interval=1)
        print(f"CPU使用率 - psutil: {psutil_cpu:.2f}%, fallback: {cpu_percent:.2f}%")
        
        # 磁盘对比
        psutil_disk = psutil.disk_usage('/')
        print(f"磁盘使用率 - psutil: {psutil_disk.percent:.2f}%, fallback: {disk_info['percent']:.2f}%")
        
    except ImportError:
        print("⚠️  psutil不可用，将使用fallback方法")
        print("建议安装psutil以获得更准确的监控数据: pip install psutil")
    print()
    
    print("=" * 50)
    print("测试完成")
    print("=" * 50)


def test_continuous_monitoring():
    """测试连续监控"""
    print("\n🔄 开始连续监控测试 (按Ctrl+C停止)...")
    
    try:
        while True:
            mem_info = get_memory_info_fallback()
            cpu_percent = get_cpu_info_fallback()
            disk_info = get_disk_info_fallback()
            
            timestamp = time.strftime("%H:%M:%S")
            print(f"[{timestamp}] 内存: {mem_info['percent']:.1f}%, "
                  f"CPU: {cpu_percent:.1f}%, "
                  f"磁盘: {disk_info['percent']:.1f}%")
            
            time.sleep(5)  # 每5秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")


if __name__ == "__main__":
    test_fallback_monitoring()
    
    # 询问是否进行连续监控测试
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        test_continuous_monitoring()
    else:
        print("\n提示: 使用 --continuous 参数进行连续监控测试")
