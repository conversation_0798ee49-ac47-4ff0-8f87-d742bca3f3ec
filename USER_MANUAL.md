# ASR服务器用户手册

## 目录
1. [系统概述](#系统概述)
2. [环境要求](#环境要求)
3. [安装部署](#安装部署)
4. [配置说明](#配置说明)
5. [启动服务](#启动服务)
6. [API接口](#api接口)
7. [WebSocket协议](#websocket协议)
8. [监控和健康检查](#监控和健康检查)
9. [故障排查](#故障排查)
10. [性能优化](#性能优化)

## 系统概述

ASR服务器是一个基于WebSocket的实时语音识别服务，支持多语种识别、语言自动检测(LID)、并发处理等功能。

### 主要特性
- **实时语音识别**: 支持流式音频数据处理
- **多语种支持**: 支持中文、英文、俄语、哈萨克语、维吾尔语等
- **语言自动检测**: 自动识别音频语言并切换对应模型
- **高并发处理**: ONNX会话池支持多个并发请求
- **系统监控**: 实时监控内存、CPU、磁盘使用情况
- **健康检查**: 提供服务健康状态检查接口

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 18.04+, CentOS 7+)
- **Python版本**: Python 3.8+
- **内存**: 最小4GB，推荐8GB+
- **存储**: 至少10GB可用空间（用于模型文件）

### 硬件要求
- **CPU**: 多核处理器，推荐8核+
- **GPU**: 可选，支持CUDA的GPU可提升性能
- **网络**: 稳定的网络连接

## 安装部署

### 1. 克隆项目
```bash
git clone <repository_url>
cd ASRServer/03_Individual_stream_asr_server
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements.txt

# 可选：安装psutil以获得更好的系统监控
pip install psutil
```

### 3. 准备模型文件
将ONNX模型文件放置在相应目录：
```
models/
├── zh/          # 中文模型
├── en/          # 英文模型
├── ru/          # 俄语模型
├── kk/          # 哈萨克语模型
├── ug/          # 维吾尔语模型
└── lid/         # 语言识别模型
```

### 4. 配置文件设置
编辑 `config.yaml` 和各语种配置文件 `config_zh.yaml`, `config_en.yaml` 等。

## 配置说明

### 主配置文件 (config.yaml)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"              # 服务监听地址
  heartbeat_interval: 30        # 心跳间隔（秒）
  max_connections: 100          # 最大并发连接数
  connection_timeout: 300       # 连接超时时间（秒）

# 音频处理配置
audio:
  valid_sample_rate_list: [44100, 16000, 8000]  # 支持的采样率
  expected_sample_rate: 16000                    # 期望采样率
  expected_sample_width: 2                       # 采样位深
  expected_sample_channels: 1                    # 声道数
  expected_data_size: 12800                      # 期望数据包大小
  max_audio_buffer_frames: 120                   # 最大缓存帧数
  buffer_cleanup_interval: 60                    # 缓存清理间隔

# LID（语种识别）配置
lid:
  enabled: true                    # 是否启用LID
  model_path: "/path/to/lid_model" # LID模型路径
  confidence_threshold: 0.7        # 置信度阈值
  max_attempts: 6                  # 最大尝试次数

# ONNX会话池配置
onnx_session_pool:
  enabled: true                    # 是否启用会话池
  max_sessions_per_model: 4        # 每个模型的最大会话数
  session_timeout: 300             # 会话超时时间（秒）
  preload_all_languages: true     # 是否预加载所有语种模型

# 系统监控配置
monitoring:
  memory_check_interval: 60        # 内存检查间隔（秒）
  memory_warning_threshold: 80     # 内存警告阈值（百分比）
  memory_critical_threshold: 90    # 内存严重阈值（百分比）
  enable_health_check: true        # 是否启用健康检查
  health_check_port: 8081         # 健康检查端口

# 支持的语种列表
supported_languages:
  - zh    # 中文
  - en    # 英文
  - ru    # 俄语
  - kk    # 哈萨克语
  - ug    # 维吾尔语

# 多语种模式配置
multi_language:
  default_language: "zh"           # 默认语种
  fallback_language: "zh"          # 回退语种
  enable_auto_switch: true         # 是否启用自动切换
```

### 语种特定配置文件 (config_zh.yaml 示例)

```yaml
# 中文模型配置
model_config:
  onnx_dir: "models/zh"
  dict_path: "models/zh/dict.txt"
  
# 特征提取配置
feature_config:
  sample_rate: 16000
  frame_length: 400
  frame_shift: 160
  
# 解码配置
decoder_config:
  beam_size: 4
  max_active: 7000
```

## 启动服务

### 单语种模式
```bash
# 启动中文识别服务
python server.py --lang zh --config config_zh.yaml

# 启动英文识别服务
python server.py --lang en --config config_en.yaml
```

### 多语种模式
```bash
# 启动多语种识别服务
python server.py --multi-lang --config config.yaml
```

### 服务启动参数
- `--host`: 服务监听地址（默认: 0.0.0.0）
- `--port`: 服务监听端口（默认: 8000）
- `--lang`: 单语种模式的语言代码
- `--multi-lang`: 启用多语种模式
- `--config`: 配置文件路径
- `--log-level`: 日志级别（DEBUG, INFO, WARNING, ERROR）

## API接口

### 1. 数据传输规则查询
```http
GET /api/transmission-rules
```

**响应示例**:
```json
{
  "audio_format": {
    "sample_rate": 16000,
    "sample_width": 2,
    "channels": 1,
    "encoding": "PCM",
    "data_encoding": "base64"
  },
  "packet_format": {
    "max_packet_size": 12800,
    "packet_interval_ms": 400,
    "timeout_seconds": 6
  },
  "protocol": {
    "websocket_endpoint": "/ws/{client_id}",
    "heartbeat_interval": 30,
    "supported_sample_rates": [44100, 16000, 8000]
  },
  "features": {
    "multi_language_support": true,
    "lid_enabled": true,
    "supported_languages": ["zh", "en", "ru", "kk", "ug"]
  }
}
```

### 2. 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": **********.0,
  "uptime_seconds": 3600,
  "active_connections": 5
}
```

## WebSocket协议

### 连接建立
```javascript
const ws = new WebSocket('ws://localhost:8000/ws/your-client-id');
```

### 消息格式

#### 1. 音频数据包
```json
{
  "type": "audio",
  "client_id": "your-client-id",
  "index": 1,
  "data": "base64_encoded_audio_data",
  "sample_rate": 16000,
  "language": "zh"  // 可选，多语种模式下指定语言
}
```

#### 2. 心跳包
```json
{
  "type": "heartbeat",
  "client_id": "your-client-id",
  "timestamp": **********.0
}
```

#### 3. 识别结果
```json
{
  "type": "result",
  "client_id": "your-client-id",
  "index": 1,
  "text": "识别结果文本",
  "confidence": 0.95,
  "language": "zh",
  "is_final": true
}
```

#### 4. 错误响应
```json
{
  "type": "error",
  "client_id": "your-client-id",
  "error_code": 4001,
  "error_message": "数据格式错误",
  "details": "音频数据解码失败",
  "timestamp": **********.0
}
```

### 错误码说明

| 错误码 | 类型 | 说明 |
|--------|------|------|
| 4001 | 客户端错误 | 数据格式错误 |
| 4002 | 客户端错误 | 参数缺失 |
| 4003 | 客户端错误 | 数据包索引错误 |
| 4004 | 客户端错误 | 音频格式不支持 |
| 4005 | 客户端错误 | 数据包过大 |
| 4006 | 客户端错误 | 心跳超时 |
| 5001 | 服务器错误 | 内部处理错误 |
| 5002 | 服务器错误 | 模型加载失败 |
| 5003 | 服务器错误 | 特征提取失败 |
| 6001 | 系统错误 | 内存不足 |
| 6002 | 系统错误 | 网络连接错误 |

## 监控和健康检查

### 监控端点 (端口8081)

#### 1. 健康状态
```http
GET http://localhost:8081/health
```

#### 2. 系统指标
```http
GET http://localhost:8081/metrics
```

#### 3. 历史指标
```http
GET http://localhost:8081/metrics/history
```

#### 4. 统计信息
```http
GET http://localhost:8081/stats
```

### 监控指标说明

- **memory_percent**: 内存使用百分比
- **cpu_percent**: CPU使用百分比
- **disk_usage_percent**: 磁盘使用百分比
- **active_connections**: 当前活跃连接数
- **total_requests**: 总请求数
- **error_count**: 错误计数

## 故障排查

### 常见问题

#### 1. 服务启动失败
**症状**: 服务无法启动，出现端口占用错误
**解决方案**:
```bash
# 检查端口占用
netstat -tlnp | grep :8000
# 杀死占用进程
kill -9 <pid>
```

#### 2. 模型加载失败
**症状**: 日志显示模型文件无法加载
**解决方案**:
- 检查模型文件路径是否正确
- 确认模型文件完整性
- 检查文件权限

#### 3. 内存使用过高
**症状**: 系统内存使用率持续上升
**解决方案**:
- 调整缓存清理参数
- 减少并发连接数
- 增加系统内存

#### 4. WebSocket连接断开
**症状**: 客户端连接频繁断开
**解决方案**:
- 检查网络稳定性
- 调整心跳间隔
- 检查防火墙设置

### 日志分析

日志文件位置: `logs/asr_server.log`

重要日志关键词:
- `ERROR`: 错误信息
- `WARNING`: 警告信息
- `client_id`: 客户端相关操作
- `Memory usage`: 内存使用情况

## 性能优化

### 1. 系统级优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 调整TCP参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
sysctl -p
```

### 2. 应用级优化
- 调整ONNX会话池大小
- 优化音频缓存参数
- 合理设置并发连接数

### 3. 硬件优化
- 使用SSD存储模型文件
- 增加内存容量
- 使用多核CPU或GPU加速

### 4. 网络优化
- 使用CDN加速模型下载
- 优化网络带宽
- 减少网络延迟

## 客户端开发示例

### Python客户端示例

```python
import asyncio
import websockets
import json
import base64
import wave

class ASRClient:
    def __init__(self, server_url, client_id):
        self.server_url = server_url
        self.client_id = client_id
        self.websocket = None

    async def connect(self):
        """连接到ASR服务器"""
        uri = f"{self.server_url}/ws/{self.client_id}"
        self.websocket = await websockets.connect(uri)
        print(f"已连接到服务器: {uri}")

    async def send_audio_file(self, audio_file_path, language="zh"):
        """发送音频文件进行识别"""
        with wave.open(audio_file_path, 'rb') as wav_file:
            frames = wav_file.readframes(wav_file.getnframes())
            sample_rate = wav_file.getframerate()

            # 分块发送音频数据
            chunk_size = 6400  # 每次发送6400字节
            index = 0

            for i in range(0, len(frames), chunk_size):
                chunk = frames[i:i + chunk_size]
                audio_data = base64.b64encode(chunk).decode('utf-8')

                message = {
                    "type": "audio",
                    "client_id": self.client_id,
                    "index": index,
                    "data": audio_data,
                    "sample_rate": sample_rate,
                    "language": language
                }

                await self.websocket.send(json.dumps(message))
                index += 1

                # 等待一小段时间模拟实时流
                await asyncio.sleep(0.4)

    async def listen_results(self):
        """监听识别结果"""
        try:
            async for message in self.websocket:
                data = json.loads(message)

                if data["type"] == "result":
                    print(f"识别结果: {data['text']}")
                    print(f"置信度: {data['confidence']}")
                    print(f"语言: {data['language']}")

                elif data["type"] == "error":
                    print(f"错误: {data['error_message']}")
                    print(f"详情: {data.get('details', '')}")

        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭")

    async def send_heartbeat(self):
        """发送心跳包"""
        while True:
            try:
                heartbeat = {
                    "type": "heartbeat",
                    "client_id": self.client_id,
                    "timestamp": time.time()
                }
                await self.websocket.send(json.dumps(heartbeat))
                await asyncio.sleep(30)  # 每30秒发送一次心跳
            except:
                break

    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()

# 使用示例
async def main():
    client = ASRClient("ws://localhost:8000", "test-client-001")

    try:
        await client.connect()

        # 启动心跳任务
        heartbeat_task = asyncio.create_task(client.send_heartbeat())

        # 启动结果监听任务
        listen_task = asyncio.create_task(client.listen_results())

        # 发送音频文件
        await client.send_audio_file("test_audio.wav", "zh")

        # 等待处理完成
        await asyncio.sleep(5)

    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript客户端示例

```javascript
class ASRClient {
    constructor(serverUrl, clientId) {
        this.serverUrl = serverUrl;
        this.clientId = clientId;
        this.websocket = null;
        this.heartbeatInterval = null;
    }

    connect() {
        return new Promise((resolve, reject) => {
            const uri = `${this.serverUrl}/ws/${this.clientId}`;
            this.websocket = new WebSocket(uri);

            this.websocket.onopen = () => {
                console.log(`已连接到服务器: ${uri}`);
                this.startHeartbeat();
                resolve();
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                reject(error);
            };

            this.websocket.onclose = () => {
                console.log('连接已关闭');
                this.stopHeartbeat();
            };
        });
    }

    handleMessage(data) {
        switch(data.type) {
            case 'result':
                console.log(`识别结果: ${data.text}`);
                console.log(`置信度: ${data.confidence}`);
                console.log(`语言: ${data.language}`);
                break;

            case 'error':
                console.error(`错误: ${data.error_message}`);
                console.error(`详情: ${data.details || ''}`);
                break;
        }
    }

    sendAudioData(audioData, index, sampleRate = 16000, language = 'zh') {
        const message = {
            type: 'audio',
            client_id: this.clientId,
            index: index,
            data: audioData, // base64编码的音频数据
            sample_rate: sampleRate,
            language: language
        };

        this.websocket.send(JSON.stringify(message));
    }

    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            const heartbeat = {
                type: 'heartbeat',
                client_id: this.clientId,
                timestamp: Date.now() / 1000
            };
            this.websocket.send(JSON.stringify(heartbeat));
        }, 30000); // 每30秒发送一次心跳
    }

    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    close() {
        this.stopHeartbeat();
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 使用示例
const client = new ASRClient('ws://localhost:8000', 'test-client-001');

client.connect().then(() => {
    console.log('连接成功，可以开始发送音频数据');

    // 这里可以添加音频录制和发送逻辑
    // 例如使用MediaRecorder API录制音频

}).catch(error => {
    console.error('连接失败:', error);
});
```

## 部署指南

### Docker部署

#### 1. 创建Dockerfile

```dockerfile
FROM python:3.8-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000 8081

# 启动命令
CMD ["python", "server.py", "--multi-lang", "--config", "config.yaml"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  asr-server:
    build: .
    ports:
      - "8000:8000"  # 主服务端口
      - "8081:8081"  # 监控端口
    volumes:
      - ./models:/app/models:ro  # 模型文件只读挂载
      - ./logs:/app/logs         # 日志目录
      - ./config.yaml:/app/config.yaml:ro
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - asr-server
    restart: unless-stopped
```

#### 3. 部署命令

```bash
# 构建和启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f asr-server

# 停止服务
docker-compose down
```

### 系统服务部署

#### 1. 创建systemd服务文件

```ini
# /etc/systemd/system/asr-server.service
[Unit]
Description=ASR Server
After=network.target

[Service]
Type=simple
User=asr
Group=asr
WorkingDirectory=/opt/asr-server
Environment=PYTHONPATH=/opt/asr-server
ExecStart=/opt/asr-server/venv/bin/python server.py --multi-lang --config config.yaml
Restart=always
RestartSec=10

# 资源限制
LimitNOFILE=65536
MemoryMax=8G

[Install]
WantedBy=multi-user.target
```

#### 2. 启用和管理服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable asr-server

# 启动服务
sudo systemctl start asr-server

# 查看状态
sudo systemctl status asr-server

# 查看日志
sudo journalctl -u asr-server -f
```

## 安全配置

### 1. 防火墙设置

```bash
# 允许ASR服务端口
sudo ufw allow 8000/tcp
sudo ufw allow 8081/tcp

# 限制监控端口只允许内网访问
sudo ufw allow from ***********/16 to any port 8081
```

### 2. SSL/TLS配置

#### nginx配置示例

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # WebSocket代理
    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查代理
    location /health {
        proxy_pass http://localhost:8000;
    }
}
```

### 3. 访问控制

```python
# 在server.py中添加IP白名单检查
ALLOWED_IPS = ['127.0.0.1', '***********/24']

@app.middleware("http")
async def ip_whitelist_middleware(request: Request, call_next):
    client_ip = request.client.host

    if not is_ip_allowed(client_ip, ALLOWED_IPS):
        return JSONResponse(
            status_code=403,
            content={"error": "Access denied"}
        )

    response = await call_next(request)
    return response
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/asr-server"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 备份配置文件
cp -r config*.yaml $BACKUP_DIR/$DATE/

# 备份日志文件
cp -r logs/ $BACKUP_DIR/$DATE/

# 备份模型文件（如果需要）
# cp -r models/ $BACKUP_DIR/$DATE/

# 压缩备份
tar -czf $BACKUP_DIR/asr-server-backup-$DATE.tar.gz -C $BACKUP_DIR $DATE

# 清理临时目录
rm -rf $BACKUP_DIR/$DATE

echo "备份完成: $BACKUP_DIR/asr-server-backup-$DATE.tar.gz"
```

### 2. 自动备份

```bash
# 添加到crontab
# 每天凌晨2点执行备份
0 2 * * * /opt/asr-server/backup.sh
```

---

## 技术支持

### 常见问题FAQ

**Q: 服务启动后无法连接WebSocket？**
A: 检查防火墙设置，确保端口8000已开放。检查服务是否正常启动。

**Q: 识别准确率不高？**
A: 确保音频质量良好，采样率为16kHz，检查模型文件是否正确加载。

**Q: 内存使用过高？**
A: 调整缓存清理参数，减少并发连接数，或增加系统内存。

**Q: 多语种切换不生效？**
A: 确保LID模型正确加载，检查置信度阈值设置。

### 联系方式

如遇到问题，请提供以下信息：
1. 系统环境信息 (`uname -a`, `python --version`)
2. 错误日志 (`logs/asr_server.log`)
3. 配置文件内容
4. 详细的复现步骤

技术支持: [技术支持邮箱或联系方式]
文档版本: v1.0
最后更新: 2024-01-01
