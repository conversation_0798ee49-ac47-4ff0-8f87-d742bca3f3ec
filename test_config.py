#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置系统测试脚本
"""

import sys
import os
import yaml
from pathlib import Path

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_global_config():
    """测试全局配置加载"""
    print("=== 测试全局配置加载 ===")
    
    # 检查config.yaml是否存在
    config_file = Path("config.yaml")
    if config_file.exists():
        print(f"✓ 全局配置文件存在: {config_file}")
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        print(f"✓ 配置内容预览: {list(config_data.keys())}")
    else:
        print(f"✗ 全局配置文件不存在: {config_file}")
    
    print()

def test_language_configs():
    """测试语种配置文件"""
    print("=== 测试语种配置文件 ===")
    
    languages = ['zh', 'en', 'ru', 'kk', 'kkin', 'ug']
    
    for lang in languages:
        config_file = Path(f"server_config_{lang}.yaml")
        if config_file.exists():
            print(f"✓ {lang}: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                print(f"  - 配置项: {len(config_data)} 个")
                if 'onnx_dir' in config_data:
                    print(f"  - 模型路径: {config_data['onnx_dir']}")
            except Exception as e:
                print(f"  ✗ 解析失败: {e}")
        else:
            print(f"✗ {lang}: {config_file} 不存在")
    
    print()

def test_error_codes():
    """测试错误码系统"""
    print("=== 测试错误码系统 ===")
    
    try:
        from modules.error_codes import ErrorCode, ErrorMessage, ErrorResponse
        print("✓ 错误码模块导入成功")
        
        # 测试错误消息
        msg = ErrorMessage.get_message(ErrorCode.INVALID_SAMPLE_RATE, 
                                     supported_rates=[16000, 44100])
        print(f"✓ 错误消息格式化: {msg}")
        
        # 测试错误响应
        response = ErrorResponse.create_error_response(
            ErrorCode.INVALID_AUDIO_FORMAT, 
            "test_client", 
            index=1
        )
        print(f"✓ 错误响应创建: code={response['code']}, state={response['state']}")
        
    except Exception as e:
        print(f"✗ 错误码系统测试失败: {e}")
    
    print()

def test_config_structure():
    """测试配置结构"""
    print("=== 测试配置结构 ===")
    
    try:
        # 不导入logger相关的模块，直接测试配置结构
        from dataclasses import dataclass, field
        from typing import List
        
        @dataclass
        class TestServerConfig:
            host: str = "0.0.0.0"
            heartbeat_interval: int = 30
        
        @dataclass  
        class TestAudioConfig:
            valid_sample_rate_list: List[int] = field(default_factory=lambda: [44100, 16000, 8000])
            expected_sample_rate: int = 16000
        
        # 测试配置类创建
        server_config = TestServerConfig()
        audio_config = TestAudioConfig()
        
        print(f"✓ 服务器配置: host={server_config.host}, interval={server_config.heartbeat_interval}")
        print(f"✓ 音频配置: rates={audio_config.valid_sample_rate_list}, rate={audio_config.expected_sample_rate}")
        
    except Exception as e:
        print(f"✗ 配置结构测试失败: {e}")
    
    print()

def main():
    """主测试函数"""
    print("配置系统测试开始...\n")
    
    test_global_config()
    test_language_configs()
    test_error_codes()
    test_config_structure()
    
    print("配置系统测试完成!")

if __name__ == "__main__":
    main()
