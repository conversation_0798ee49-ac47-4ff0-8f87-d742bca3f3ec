#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LID集成功能测试脚本
测试LID管理器和ConnectionManager的集成功能
"""

import sys
import os
import torch
import numpy as np

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.lid_manager import LIDManager
from modules.logger import logger

def test_lid_manager():
    """测试LID管理器基本功能"""
    print("=" * 50)
    print("测试LID管理器基本功能")
    print("=" * 50)
    
    # 测试不存在的模型路径
    try:
        lid_manager = LIDManager("/nonexistent/path")
        print("❌ 应该抛出异常但没有抛出")
        return False
    except Exception as e:
        print(f"✅ 正确处理不存在的模型路径: {e}")
    
    # 测试空路径
    try:
        lid_manager = LIDManager("")
        print(f"✅ 空路径处理: 可用性={lid_manager.is_available()}")
        if lid_manager.is_available():
            print("❌ 空路径应该不可用")
            return False
    except Exception as e:
        print(f"✅ 空路径正确抛出异常: {e}")
    
    # 测试模拟音频数据
    print("\n测试模拟音频数据处理...")
    try:
        lid_manager = LIDManager("")  # 空路径，不加载模型
        
        # 创建模拟音频数据 (16000 Hz, 1秒)
        sample_rate = 16000
        duration = 1.0
        audio_samples = int(sample_rate * duration)
        audio_data = torch.randn(audio_samples)  # 随机噪声
        
        # 测试VAD检测（应该返回False，因为是随机噪声）
        has_speech = lid_manager.detect_speech(audio_data, sample_rate)
        print(f"✅ VAD检测结果（随机噪声）: {has_speech}")
        
        # 测试语种识别（应该返回默认结果）
        lid_result = lid_manager.predict_language(audio_data, sample_rate)
        print(f"✅ LID结果: {lid_result}")
        
    except Exception as e:
        print(f"❌ 模拟音频测试失败: {e}")
        return False
    
    return True

def test_connection_manager_integration():
    """测试ConnectionManager集成"""
    print("\n" + "=" * 50)
    print("测试ConnectionManager集成")
    print("=" * 50)
    
    try:
        # 这里只能做基本的导入测试，因为需要完整的服务器环境
        from modules.connect import ConnectionManager
        print("✅ ConnectionManager导入成功")
        
        # 检查是否添加了LID相关方法
        methods = dir(ConnectionManager)
        lid_methods = [m for m in methods if 'lid' in m.lower() or '_process_lid' in m or '_perform_lid' in m]
        print(f"✅ LID相关方法: {lid_methods}")
        
        if '_process_lid' not in methods or '_perform_lid' not in methods:
            print("❌ 缺少LID处理方法")
            return False
            
    except Exception as e:
        print(f"❌ ConnectionManager集成测试失败: {e}")
        return False
    
    return True

def test_config_integration():
    """测试配置集成"""
    print("\n" + "=" * 50)
    print("测试配置集成")
    print("=" * 50)
    
    try:
        from modules.config import parse_args
        
        # 模拟命令行参数
        original_argv = sys.argv
        sys.argv = ['test_script', 'zh', '8080']
        
        try:
            args = parse_args()
            print(f"✅ 配置解析成功")
            print(f"✅ LID模型路径配置: {getattr(args, 'lid_model_path', 'NOT_FOUND')}")
            
            if not hasattr(args, 'lid_model_path'):
                print("❌ 缺少lid_model_path配置")
                return False
                
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        return False
    
    return True

def test_server_integration():
    """测试服务器集成"""
    print("\n" + "=" * 50)
    print("测试服务器集成")
    print("=" * 50)
    
    try:
        # 检查server.py是否正确导入LIDManager
        import server
        print("✅ server.py导入成功")
        
        # 检查是否有LID_MANAGER全局变量
        if hasattr(server, 'LID_MANAGER'):
            print("✅ 找到LID_MANAGER全局变量")
        else:
            print("❌ 缺少LID_MANAGER全局变量")
            return False
            
    except Exception as e:
        print(f"❌ 服务器集成测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始LID集成功能测试...")
    print("注意：这是基本的集成测试，完整测试需要实际的LID模型文件")
    
    tests = [
        ("LID管理器基本功能", test_lid_manager),
        ("ConnectionManager集成", test_connection_manager_integration),
        ("配置集成", test_config_integration),
        ("服务器集成", test_server_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name}: 失败")
        except Exception as e:
            print(f"\n❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有基本集成测试通过！")
        print("\n下一步:")
        print("1. 准备LID模型文件并配置正确的路径")
        print("2. 启动服务器进行实际测试")
        print("3. 使用客户端发送多语种音频进行端到端测试")
        return True
    else:
        print("⚠️  部分测试失败，请检查集成代码")
        return False

if __name__ == "__main__":
    main()
