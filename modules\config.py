#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理全局配置和语种特定配置
"""

import yaml
import argparse
import os
import sys
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path

from modules.logger import logger
from modules.error_codes import ErrorCode, ErrorMessage


@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    heartbeat_interval: int = 30
    max_connections: int = 100
    connection_timeout: int = 300


@dataclass
class AudioConfig:
    """音频处理配置"""
    valid_sample_rate_list: List[int] = field(default_factory=lambda: [44100, 16000, 8000])
    expected_sample_rate: int = 16000
    expected_sample_width: int = 2
    expected_sample_channels: int = 1
    expected_data_size: int = 12800
    expected_time_interval: int = 6
    max_audio_buffer_frames: int = 120
    buffer_cleanup_interval: int = 60


@dataclass
class LIDConfig:
    """语种识别配置"""
    enabled: bool = True
    model_path: str = ""
    min_duration: float = 0.4
    max_duration: float = 2.4
    detection_interval: float = 0.4
    max_attempts: int = 6
    confidence_threshold: float = 0.7
    silence_threshold: int = 10
    vad_enabled: bool = True


@dataclass
class ONNXSessionPoolConfig:
    """ONNX会话池配置"""
    enabled: bool = True
    max_sessions_per_model: int = 4
    session_timeout: int = 300
    preload_all_languages: bool = True


@dataclass
class MonitoringConfig:
    """性能监控配置"""
    memory_check_interval: int = 60
    memory_warning_threshold: int = 80
    memory_critical_threshold: int = 90
    enable_health_check: bool = True
    health_check_port: int = 8081


@dataclass
class GlobalConfig:
    """全局配置"""
    server: ServerConfig = field(default_factory=ServerConfig)
    audio: AudioConfig = field(default_factory=AudioConfig)
    lid: LIDConfig = field(default_factory=LIDConfig)
    onnx_session_pool: ONNXSessionPoolConfig = field(default_factory=ONNXSessionPoolConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    supported_languages: List[str] = field(default_factory=lambda: ["zh", "en", "ru", "kk", "kkin", "ug"])
    default_language: str = "zh"
    fallback_language: str = "zh"


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        self.global_config: Optional[GlobalConfig] = None
        self.language_configs: Dict[str, Any] = {}
        self._config_cache: Dict[str, Any] = {}

    def load_global_config(self, config_path: str = "config.yaml") -> GlobalConfig:
        """
        加载全局配置
        Args:
            config_path: 配置文件路径
        Returns:
            GlobalConfig: 全局配置对象
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                logger.warning(f"全局配置文件不存在: {config_path}，使用默认配置")
                self.global_config = GlobalConfig()
                return self.global_config

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            # 解析配置数据
            self.global_config = self._parse_global_config(config_data)
            logger.info(f"成功加载全局配置: {config_path}")
            return self.global_config

        except Exception as e:
            logger.error(f"加载全局配置失败: {e}")
            self.global_config = GlobalConfig()
            return self.global_config

    def _parse_global_config(self, config_data: Dict[str, Any]) -> GlobalConfig:
        """解析全局配置数据"""
        config = GlobalConfig()

        # 解析服务器配置
        if 'server' in config_data:
            server_data = config_data['server']
            config.server = ServerConfig(
                host=server_data.get('host', config.server.host),
                heartbeat_interval=server_data.get('heartbeat_interval', config.server.heartbeat_interval),
                max_connections=server_data.get('max_connections', config.server.max_connections),
                connection_timeout=server_data.get('connection_timeout', config.server.connection_timeout)
            )

        # 解析音频配置
        if 'audio' in config_data:
            audio_data = config_data['audio']
            config.audio = AudioConfig(
                valid_sample_rate_list=audio_data.get('valid_sample_rate_list', config.audio.valid_sample_rate_list),
                expected_sample_rate=audio_data.get('expected_sample_rate', config.audio.expected_sample_rate),
                expected_sample_width=audio_data.get('expected_sample_width', config.audio.expected_sample_width),
                expected_sample_channels=audio_data.get('expected_sample_channels', config.audio.expected_sample_channels),
                expected_data_size=audio_data.get('expected_data_size', config.audio.expected_data_size),
                expected_time_interval=audio_data.get('expected_time_interval', config.audio.expected_time_interval),
                max_audio_buffer_frames=audio_data.get('max_audio_buffer_frames', config.audio.max_audio_buffer_frames),
                buffer_cleanup_interval=audio_data.get('buffer_cleanup_interval', config.audio.buffer_cleanup_interval)
            )

        # 解析LID配置
        if 'lid' in config_data:
            lid_data = config_data['lid']
            config.lid = LIDConfig(
                enabled=lid_data.get('enabled', config.lid.enabled),
                model_path=lid_data.get('model_path', config.lid.model_path),
                min_duration=lid_data.get('min_duration', config.lid.min_duration),
                max_duration=lid_data.get('max_duration', config.lid.max_duration),
                detection_interval=lid_data.get('detection_interval', config.lid.detection_interval),
                max_attempts=lid_data.get('max_attempts', config.lid.max_attempts),
                confidence_threshold=lid_data.get('confidence_threshold', config.lid.confidence_threshold),
                silence_threshold=lid_data.get('silence_threshold', config.lid.silence_threshold),
                vad_enabled=lid_data.get('vad_enabled', config.lid.vad_enabled)
            )

        # 解析ONNX会话池配置
        if 'onnx_session_pool' in config_data:
            pool_data = config_data['onnx_session_pool']
            config.onnx_session_pool = ONNXSessionPoolConfig(
                enabled=pool_data.get('enabled', config.onnx_session_pool.enabled),
                max_sessions_per_model=pool_data.get('max_sessions_per_model', config.onnx_session_pool.max_sessions_per_model),
                session_timeout=pool_data.get('session_timeout', config.onnx_session_pool.session_timeout),
                preload_all_languages=pool_data.get('preload_all_languages', config.onnx_session_pool.preload_all_languages)
            )

        # 解析监控配置
        if 'monitoring' in config_data:
            monitoring_data = config_data['monitoring']
            config.monitoring = MonitoringConfig(
                memory_check_interval=monitoring_data.get('memory_check_interval', config.monitoring.memory_check_interval),
                memory_warning_threshold=monitoring_data.get('memory_warning_threshold', config.monitoring.memory_warning_threshold),
                memory_critical_threshold=monitoring_data.get('memory_critical_threshold', config.monitoring.memory_critical_threshold),
                enable_health_check=monitoring_data.get('enable_health_check', config.monitoring.enable_health_check),
                health_check_port=monitoring_data.get('health_check_port', config.monitoring.health_check_port)
            )

        # 解析其他配置
        config.supported_languages = config_data.get('supported_languages', config.supported_languages)
        if 'multi_language' in config_data:
            ml_data = config_data['multi_language']
            config.default_language = ml_data.get('default_language', config.default_language)
            config.fallback_language = ml_data.get('fallback_language', config.fallback_language)

        return config


    def load_language_config(self, lang_code: str) -> Dict[str, Any]:
        """
        加载语种特定配置
        Args:
            lang_code: 语种代码
        Returns:
            Dict: 语种配置
        """
        if lang_code in self._config_cache:
            return self._config_cache[lang_code]

        try:
            if lang_code == 'multi':
                # 多语种模式，使用中文配置作为基础配置
                config_file = f"server_config_zh.yaml"
            else:
                config_file = f"server_config_{lang_code}.yaml"

            config_path = Path(config_file)
            if not config_path.exists():
                # 尝试在脚本目录下查找
                script_dir = Path(sys.argv[0]).parent
                config_path = script_dir / config_file

            if not config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file}")

            logger.info(f"加载语种配置: {config_path}")
            with open(config_path, 'r', encoding='utf-8') as f:
                params = yaml.safe_load(f)

            # 缓存配置
            self._config_cache[lang_code] = params
            self.language_configs[lang_code] = params

            return params

        except Exception as e:
            logger.error(f"加载语种配置失败 {lang_code}: {e}")
            raise

    def get_merged_config(self, lang_code: str) -> Dict[str, Any]:
        """
        获取合并后的配置（全局配置 + 语种配置）
        Args:
            lang_code: 语种代码
        Returns:
            Dict: 合并后的配置
        """
        if self.global_config is None:
            self.load_global_config()

        # 获取语种特定配置
        lang_config = self.load_language_config(lang_code)

        # 合并配置
        merged_config = {}

        # 添加全局配置中的音频参数
        merged_config.update({
            'host': self.global_config.server.host,
            'heartbeat_interval': self.global_config.server.heartbeat_interval,
            'valid_sample_rate_list': self.global_config.audio.valid_sample_rate_list,
            'expected_sample_rate': self.global_config.audio.expected_sample_rate,
            'expected_sample_width': self.global_config.audio.expected_sample_width,
            'expected_sample_channels': self.global_config.audio.expected_sample_channels,
            'expected_data_size': self.global_config.audio.expected_data_size,
            'expected_time_interval': self.global_config.audio.expected_time_interval,
            'lid_model_path': self.global_config.lid.model_path,
        })

        # 添加语种特定配置（会覆盖全局配置中的同名项）
        merged_config.update(lang_config)

        return merged_config


# 全局配置管理器实例
config_manager = ConfigManager()


def read_params(lang_code: str) -> Dict[str, Any]:
    """
    读取配置参数（保持向后兼容）
    Args:
        lang_code: 语种代码，例如 'zh' 或 'multi'
    Returns:
        Dict: 配置参数
    """
    return config_manager.get_merged_config(lang_code)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="在线ASR服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  单语种模式: python server.py zh 8080
  多语种模式: python server.py multi 8080
  调试模式:   python server.py zh 8080 debug
        """
    )

    # 模型相关参数
    parser.add_argument("--onnx_dir", type=str,
                        default="/ws/zh_model/onnx_cpu_stream_250403_v3.1",
                        help="ONNX模型目录路径")
    parser.add_argument("--onnx_config", type=str, default="",
                        help="ONNX模型配置文件路径")

    # 解码参数
    parser.add_argument('--chunk_size', type=int, default=16,
                        help='解码块大小')
    parser.add_argument('--num_left_chunks', type=int, default=16,
                        help='缓存块数量')
    parser.add_argument('--reverse_weight', type=float, default=0.5,
                        help='注意力重打分中的反向权重')
    parser.add_argument('--mode',
                        choices=['ctc_greedy_search', 'ctc_prefix_beam_search', 'attention_rescoring'],
                        default='ctc_prefix_beam_search',
                        help='解码模式')

    # 特征和上下文参数
    parser.add_argument('--context_list_path', type=str, default='',
                        help='上下文列表路径')
    parser.add_argument('--context_graph_score', type=float, default=40,
                        help='上下文图偏置分数')
    parser.add_argument("--feat_type", type=str, default="fbank",
                        help="特征类型")

    # 模型优化参数
    parser.add_argument('--fp16', action='store_true',
                        help="使用FP16模型")
    parser.add_argument('--quant', action='store_true',
                        help="使用量化模型")

    # 设备参数
    parser.add_argument('--device', type=str, choices=['cpu', 'gpu', 'npu'],
                        default='cpu', help='计算设备')
    parser.add_argument('--device_id', type=int, default=0,
                        help='设备ID')

    # 多语种参数
    parser.add_argument('--lid_model_path', type=str, default='',
                        help='语种识别模型路径')
    parser.add_argument('--multi_lang', action='store_true', default=False,
                        help='启用多语种模式')

    # 位置参数
    parser.add_argument('lang_code',
                        choices=['zh', 'en', 'ru', 'kk', 'kkin', 'ug', 'multi'],
                        help='语种代码')
    parser.add_argument('port', type=int, default=11222, help='服务端口')
    parser.add_argument('args', nargs='*', help='额外参数')

    args = parser.parse_args()

    try:
        # 加载全局配置
        config_manager.load_global_config()

        # 设置多语种模式标志
        if args.lang_code == 'multi':
            args.multi_lang = True

        # 合并配置文件参数
        params = read_params(args.lang_code)
        for key, value in params.items():
            setattr(args, key, value)

        # 设置路径参数（单语种模式）
        if not args.multi_lang:
            args.dict_path = os.path.join(args.onnx_dir, 'units.txt')
            if not os.path.exists(args.dict_path):
                raise FileNotFoundError(f"词表文件不存在: {args.dict_path}")

            if not args.onnx_config:
                args.onnx_config = os.path.join(args.onnx_dir, 'train.yaml')
            if not args.context_list_path:
                args.context_list_path = os.path.join(args.onnx_dir, 'hotwords.txt')

        # 验证配置
        _validate_args(args)

        return args

    except Exception as e:
        logger.error(f"配置解析失败: {e}")
        raise


def _validate_args(args) -> None:
    """验证配置参数"""
    # 验证端口范围
    if not (1024 <= args.port <= 65535):
        raise ValueError(f"端口号无效: {args.port}，应在1024-65535范围内")

    # 验证模型目录
    if not args.multi_lang and not os.path.exists(args.onnx_dir):
        raise FileNotFoundError(f"模型目录不存在: {args.onnx_dir}")

    # 验证LID模型路径
    if args.lid_model_path and not os.path.exists(args.lid_model_path):
        logger.warning(f"LID模型路径不存在: {args.lid_model_path}")

    logger.info("配置参数验证通过")

class ModelConfigManager:
    """模型配置管理器（用于ONNX模型配置）"""

    def __init__(self, args, onnx_metadatas):
        self.args = args
        self.metadatas = onnx_metadatas
        self.configs = self.load_configs(args)

    def load_configs(self, args):
        with open(args.onnx_config, 'r') as f:
            configs_ = yaml.safe_load(f)

        feat_type = args.feat_type
        feat_conf = configs_['dataset_conf'][f'{feat_type}_conf']
        encoder_conf = configs_['encoder_conf']
        onnx_encoder_conf = self.metadatas['encoder']
        
        logger.debug(f"onnx_encoder_conf: {onnx_encoder_conf}")

        configs = {
            "feat_configs":{
                "feat_type": feat_type,
                "num_mel_bins": feat_conf['num_mel_bins'],
                "frame_length": feat_conf['frame_length'],
                "frame_shift": feat_conf['frame_shift'],
                "dither": feat_conf['dither'],
                "n_fft": 400,
                "hop_length": 160,
            },
            'batch' : int(onnx_encoder_conf.get("batch", 1)),
            'chunk_size' : int(onnx_encoder_conf.get('chunk_size', args.chunk_size)),
            'left_chunks' : int(onnx_encoder_conf.get('left_chunks', args.num_left_chunks)),
            'reverse_weight' : float(onnx_encoder_conf.get('reverse_weight', args.reverse_weight)),
            'output_size' : int(onnx_encoder_conf.get('output_size', encoder_conf['output_size'])),
            'num_blocks' : int(encoder_conf.get('num_blocks', encoder_conf['num_blocks'])),
            'cnn_module_kernel' : int(onnx_encoder_conf.get('cnn_module_kernel', encoder_conf['cnn_module_kernel'])),
            'head' : int(onnx_encoder_conf.get('head', encoder_conf['attention_heads'])),
            'feature_size' : int(onnx_encoder_conf.get('feature_size', configs_['input_dim'])),
            'vocab_size' : int(onnx_encoder_conf.get('vocab_size', configs_['output_dim'])),
            'decoding_window' : int(onnx_encoder_conf.get("decoding_window", 67)),
            'subsampling_rate' : int(onnx_encoder_conf.get("subsampling_rate", 4)),
            'right_context' : int(onnx_encoder_conf.get("right_context", 7)),
            'context_list_path': args.context_list_path,
            'context_graph_score': args.context_graph_score

        }
        logger.debug(f"加载的模型配置: {configs}")
        return configs
