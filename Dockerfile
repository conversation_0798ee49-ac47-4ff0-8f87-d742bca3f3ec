FROM debian:stable-slim

# 设置环境变量
ENV PATH="/opt/conda/bin:$PATH"

# 设置工作目录
WORKDIR /ws
COPY ./requirements.txt /ws/requirements.txt
COPY ./pkgs/Miniconda3-py38_23.11.0-1-Linux-x86_64.sh /ws/pkgs/Miniconda3-py38_23.11.0-1-Linux-x86_64.sh

# 安装系统依赖并清理缓存
RUN sed -i 's@http://archive.debian.org@http://mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's@http://deb.debian.org@http://mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources && \
    apt-get update

RUN apt-get install -y --no-install-recommends --fix-missing \
    python3-dev \
    build-essential \
    ffmpeg \
    vim \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /root/.cache

# 安装 Miniconda3（适配 arm64 的 Python 3.8 环境）
# wget https://repo.anaconda.com/miniconda/Miniconda3-py38_23.11.0-1-Linux-x86_64.sh
RUN bash /ws/pkgs/Miniconda3-py38_23.11.0-1-Linux-x86_64.sh -b -p /opt/conda && \
    /opt/conda/bin/conda init bash && \
    rm /ws/pkgs/Miniconda3-py38_23.11.0-1-Linux-x86_64.sh && \
    echo "conda activate base" >> ~/.bashrc

# 安装 PyTorch 核心依赖
RUN pip config set global.index-url http://**********/repository/tsinghua.pypi/simple/
RUN pip config set install.trusted-host **********

RUN pip install torch==2.1.2 torchaudio==2.1.2

# 安装其他依赖
RUN pip install --upgrade pip setuptools && pip install -r /ws/requirements.txt
COPY /ws/offline_asr_server_batch/utils/swig_decoders-1.1-py3.8-linux-x86_64.egg/ /opt/conda/lib/python3.8/site-packages/swig_decoders-1.1-py3.8-linux-x86_64.egg/