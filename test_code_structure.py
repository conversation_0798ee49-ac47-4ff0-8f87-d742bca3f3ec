#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码结构验证脚本
验证LID集成的代码结构是否正确
"""

import os
import re

def test_lid_manager_file():
    """测试LID管理器文件"""
    print("=" * 50)
    print("测试LID管理器文件结构")
    print("=" * 50)
    
    file_path = "modules/lid_manager.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键类和方法
    checks = [
        ("class LIDManager", "LIDManager类定义"),
        ("def __init__", "构造函数"),
        ("def is_available", "可用性检查方法"),
        ("def detect_speech", "语音检测方法"),
        ("def predict_language", "语种识别方法"),
        ("import torch", "torch导入"),
        ("import torchaudio", "torchaudio导入"),
        ("webrtcvad", "VAD功能"),
    ]
    
    for pattern, description in checks:
        if pattern in content:
            print(f"✅ {description}")
        else:
            print(f"❌ 缺少: {description}")
            return False
    
    return True

def test_connection_manager_modifications():
    """测试ConnectionManager修改"""
    print("\n" + "=" * 50)
    print("测试ConnectionManager修改")
    print("=" * 50)
    
    file_path = "modules/connect.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查LID集成相关修改
    checks = [
        ("from modules.lid_manager import LIDManager", "LIDManager导入"),
        ("lid_manager=None", "构造函数LID参数"),
        ("lid_enabled", "LID启用状态"),
        ("detected_language", "检测语种字段"),
        ("language_confidence", "语种置信度字段"),
        ("audio_buffer", "音频缓存字段"),
        ("def _process_lid", "LID处理方法"),
        ("def _perform_lid", "LID执行方法"),
        ("response\\[\"language\"\\]", "响应中的语种字段"),
    ]
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✅ {description}")
        else:
            print(f"❌ 缺少: {description}")
            return False
    
    return True

def test_server_modifications():
    """测试服务器修改"""
    print("\n" + "=" * 50)
    print("测试服务器修改")
    print("=" * 50)
    
    file_path = "server.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查服务器LID集成
    checks = [
        ("from modules.lid_manager import LIDManager", "LIDManager导入"),
        ("LID_MANAGER", "LID_MANAGER全局变量"),
        ("lid_model_path", "LID模型路径配置"),
        ("LIDManager\\(args.lid_model_path\\)", "LID管理器初始化"),
    ]
    
    for pattern, description in checks:
        if re.search(pattern, content):
            print(f"✅ {description}")
        else:
            print(f"❌ 缺少: {description}")
            return False
    
    return True

def test_config_modifications():
    """测试配置修改"""
    print("\n" + "=" * 50)
    print("测试配置修改")
    print("=" * 50)
    
    file_path = "modules/config.py"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查配置参数
    if "--lid_model_path" in content:
        print("✅ LID模型路径参数")
    else:
        print("❌ 缺少LID模型路径参数")
        return False
    
    return True

def test_config_files():
    """测试配置文件"""
    print("\n" + "=" * 50)
    print("测试配置文件")
    print("=" * 50)
    
    config_files = [
        "server_config_zh.yaml",
        "server_config_en.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "lid_model_path:" in content:
                print(f"✅ {config_file}: 包含LID配置")
            else:
                print(f"❌ {config_file}: 缺少LID配置")
                return False
        else:
            print(f"⚠️  {config_file}: 文件不存在")
    
    return True

def test_requirements():
    """测试依赖文件"""
    print("\n" + "=" * 50)
    print("测试依赖文件")
    print("=" * 50)
    
    if os.path.exists("requirements.txt"):
        with open("requirements.txt", 'r') as f:
            content = f.read()
        
        required_deps = ["torch", "torchaudio"]
        for dep in required_deps:
            if dep in content:
                print(f"✅ 依赖: {dep}")
            else:
                print(f"❌ 缺少依赖: {dep}")
                return False
        
        return True
    else:
        print("❌ requirements.txt不存在")
        return False

def main():
    """主测试函数"""
    print("LID集成代码结构验证")
    print("检查所有必要的代码修改是否已完成")
    
    tests = [
        ("LID管理器文件", test_lid_manager_file),
        ("ConnectionManager修改", test_connection_manager_modifications),
        ("服务器修改", test_server_modifications),
        ("配置修改", test_config_modifications),
        ("配置文件", test_config_files),
        ("依赖文件", test_requirements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name}: 失败")
        except Exception as e:
            print(f"\n❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"代码结构验证结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 LID集成代码结构验证通过！")
        print("\n✅ 集成完成的功能:")
        print("  • LID管理器模块 (modules/lid_manager.py)")
        print("  • ConnectionManager LID集成")
        print("  • 服务器LID支持")
        print("  • 配置文件LID参数")
        print("  • WebSocket接口语种字段")
        print("  • 依赖文件更新")
        
        print("\n📋 使用说明:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 准备LID模型文件")
        print("3. 配置LID模型路径 (server_config_*.yaml)")
        print("4. 启动服务: python server.py zh 8080")
        print("5. 客户端可选择启用/禁用LID功能")
        
        print("\n🔧 LID功能特性:")
        print("  • 自动语种识别 (0.4s-2.4s渐进式)")
        print("  • VAD语音检测")
        print("  • 多语种ASR模型支持")
        print("  • WebSocket接口兼容")
        print("  • 可选启用/禁用")
        
        return True
    else:
        print("⚠️  代码结构验证失败，请检查集成代码")
        return False

if __name__ == "__main__":
    main()
