#! /user/bin/env python
#! -*- coding: utf-8 -*-
# File    :  asr_decoder.py
# Time    :  2025/04/03 13:34:46
# Author  :  lh
# Version :  1.0
# Description:  

from typing import Dict, List, Optional, Tuple
import math, os
import numpy as np
import torch
import torch.nn.functional as F
import onnx
import onnxruntime as ort

from utils.context_graph import ContextGraph
from utils.search import ctc_prefix_beam_search
from modules.logger import logger
from modules.symbol_table import SymbolTable

ONNX_SESSIONS = {}


def get_ep_list(device, device_id):
    use_cuda = False
    if device == 'gpu':
        os.environ['CUDA_VISIBLE_DEVICES'] = str(device_id)
        use_cuda = device_id >= 0 and torch.cuda.is_available()
        logger.debug(f"Using cuda, device id: {device_id}")
    use_cann = False
    if device == 'npu':
        import torch_npu
        os.environ['ASCEND_RT_VISIBLE_DEVICES'] = str(device_id)
        use_cann = device_id >= 0 and torch_npu.npu.is_available()
        logger.debug(f"Using cann, device id: {device_id}")
    if use_cuda:
        EP_list = ['CUDAExecutionProvider', 'CPUExecutionProvider']
    elif use_cann:
        EP_list = [
            (
                "CANNExecutionProvider",
                {
                    "device_id": device_id,
                    "arena_extend_strategy": "KNextPowerOfTwo",
                    "npu_mem_list": 2 * 1024 * 1024 * 1024,
                    "op_select_impl_mode": "high_performance",
                    "optypelist_for_implmode": "Gelu",
                    "enable_cann_graph": True
                },
            ), 
            'CPUExecutionProvider'
        ]
    else:
        EP_list = ['CPUExecutionProvider']
    logger.debug(EP_list)
    return EP_list

def load_onnx(onnx_dir, fp16, quant, mode, device, device_id):
    global ONNX_SESSIONS
    logger.info(f"加载模型: {onnx_dir}, fp16: {fp16}, quant: {quant}, device: {device}")

    # models to load
    models = ["encoder", "ctc"]
    if mode == "attention_rescoring":
        models.append("decoder")
    
    # EP list
    EP_list = get_ep_list(device, device_id)

    # load onnx
    metadatas = {}
    for m in models:
        # 默认模型
        path = os.path.join(onnx_dir, f"{m}.onnx")

        # 参数指定--fp16 且存在fp16模型
        path_fp16 = os.path.join(onnx_dir, f"{m}_fp16.onnx")
        if fp16 and os.path.exists(path_fp16):
            path = path_fp16
        # 参数指定--quant 且存在quant模型
        path_quant = os.path.join(onnx_dir, f"{m}.quant.onnx")
        if quant and os.path.exists(path_quant):
            path = path_quant
        
        # 若不加载fp16或者quant模型，至少要存在默认模型
        assert os.path.exists(path), f"File not exists: {path}"

        # 1. get metadata info
        metadata = {}
        model = onnx.load(path)
        for prop in model.metadata_props:
            metadata[prop.key] = prop.value
        metadatas[m] = metadata

        # 2. get input names
        input_names = [node.name for node in model.graph.input]
        # 3. get session
        session = ort.InferenceSession(path, providers=EP_list)
        ONNX_SESSIONS[m] = {
            "session": session,
            "input_names": input_names,
            }
        
    return metadatas

def to_numpy(tensor):
    if tensor.requires_grad:
        return tensor.detach().cpu().numpy()
    else:
        return tensor.cpu().numpy()

class Encoder:
    def __init__(self, configs: dict):
        """Encoder 初始化类,用于初始化 ONNX 会话和相关参数。
        Args:
            configs (dict): 配置参数字典,包含模型相关的配置信息。
        """
        self.configs = configs
        self.session = ONNX_SESSIONS['encoder']['session']
        self.input_names = ONNX_SESSIONS['encoder']['input_names']

        self.offset = 0
        self.outputs = []
        self.chunk_size = configs["chunk_size"]
        self.right_context = configs['right_context']
        self.required_cache_size = self.chunk_size * configs["left_chunks"]
        
        self.att_cache = torch.zeros((
                                    configs['num_blocks'], 
                                    configs['head'], 
                                    self.required_cache_size,
                                    configs['output_size'] // configs['head'] * 2))
        self.att_mask = torch.ones((
                                    configs['batch'],
                                    1, 
                                    self.required_cache_size + configs['chunk_size']), dtype=torch.bool)
        self.att_mask[:, :, :self.required_cache_size] = 0
        
        self.cnn_cache = torch.zeros((
                                    configs['num_blocks'], 
                                    configs['batch'], 
                                    configs['output_size'],
                                    configs['cnn_module_kernel'] - 1))
        

    def __del__(self):
        """显式释放 Encoder 类的资源。"""
        logger.info(f"Encoder 显式释放资源")
        if hasattr(self, 'session'):
            self.session = None
        if hasattr(self, 'cnn_cache'): 
            self.cnn_cache = None
        if hasattr(self, 'att_cache'): 
            self.att_cache = None
        if hasattr(self, 'att_mask'): 
            self.cnn_mask = None
        # del self.session
        # del self.cnn_cache
        # del self.att_cache
        # del self.att_mask
        self.outputs.clear()

    def ort_run(self, chunk: torch.Tensor, chunk_index: int) -> torch.Tensor:
        """使用 ONNX 会话运行编码器推理。
        Args:
            chunk (torch.Tensor): 输入的语音特征张量,形状为 [batch_size, chunk_size, feature_dim]。
            chunk_index (int): 当前处理的 chunk 索引。
        Returns:
            torch.Tensor: 编码器输出的张量,形状为 [batch_size, output_frame_num, hidden_dim]。
        """
        # 1. chunk
        onnx_chunk = to_numpy(chunk)

        # 2. offset
        onnx_offset = np.array((self.offset + 256)).astype(np.int64)
        
        # 3. required_cache_size
        onnx_required_cache_size = np.array((self.required_cache_size)).astype(np.int64)
        
        # 4. att_cache 
        cache_t1 = min(self.required_cache_size, self.offset)
        onnx_att_cache = to_numpy(self.att_cache)

        # 5. cnn_cache
        onnx_cnn_cache = to_numpy(self.cnn_cache)
        
        # 6. att_mask  ( 只是 att_cache 用的, True 的长度是当前att_cacahe + chunk_size)
        cache_t1 = min(self.required_cache_size, self.offset)
        att_mask = self.att_mask[:, :, :]
        att_mask[:, :, -(cache_t1+self.chunk_size):] = 1

        # logger.debug(f"构造onnx输入")
        # logger.debug(f"第{chunk_index}个chunk")
        # logger.debug(f"当前 chunk {chunk.shape}")
        # logger.debug(f"当前 offset {self.offset}")
        # logger.debug(f"当前 cnn_cache {self.cnn_cache.shape}")
        # logger.debug(f"截取 att_mask {-(cache_t1+self.chunk_size)}:")
        # logger.debug(f"当前 att_mask {att_mask.shape}:")
        onnx_att_mask = to_numpy(att_mask)

        ort_inputs = {
            'chunk': onnx_chunk,
            'offset': onnx_offset,
            'required_cache_size': onnx_required_cache_size,
            'att_cache': onnx_att_cache,
            'cnn_cache': onnx_cnn_cache,
            'att_mask': onnx_att_mask
        }
        for k in list(ort_inputs):
            if k not in self.input_names:
                ort_inputs.pop(k)
        
        # import pickle
        # with open('/ws/MODELS/online_onnx_zh/ort_inputs.pkl', 'wb') as f:
        #     pickle.dump(ort_inputs, f)

        try:
            ort_outs = self.session.run(None, ort_inputs)
            onnx_output, onnx_att_cache, onnx_cnn_cache = ort_outs[0], ort_outs[1], ort_outs[2]

            # with open('/ws/MODELS/online_onnx_zh/ort_outs.pkl', 'wb') as f:
            #     pickle.dump(ort_outs, f)
            # exit(0)

            logger.debug(f"获取onnx输出")
            logger.debug(f"onnx_output: {onnx_output.shape}")
            logger.debug(f"输出att_cache: {onnx_att_cache.shape}")
            logger.debug(f"输出cnn_cache: {onnx_cnn_cache.shape}")
            # 更新缓存
            cache_t1 = onnx_att_cache.shape[2]
            self.outputs.append(torch.from_numpy(onnx_output))
            self.att_cache = torch.from_numpy(onnx_att_cache)
            self.cnn_cache = torch.from_numpy(onnx_cnn_cache)
            self.offset += onnx_output.shape[1]
        
        except Exception as e:
            logger.debug(f"encoder.innx 请求错误! {e}")

        accm_outputs = torch.cat(self.outputs, dim=1)

        return accm_outputs
        
class CTCPrefixBeamSearch:
    def __init__(self, configs: dict, symbol_table_dict: Dict):
        """CTC 前缀集束搜索初始化类。
        Args:
            configs (dict): 配置参数字典,包含模型相关的配置信息。
        """
        self.session = ONNX_SESSIONS['ctc']['session']
        self.tokens = []
        self.times = []
        self.offset = 0
        if os.path.exists(configs["context_list_path"]):
            self.context_graph = ContextGraph(
                context_list_path=configs["context_list_path"],
                symbol_table=symbol_table_dict,
                bpe_model=None,
                context_score=configs["context_graph_score"])
        else:
            self.context_graph = None
        self.ctc_beam_search_cache = []  # 存放前一个chunk的输入
        self.last_chunk_tokens = []
        self.last_chunk_times = []

    def __del__(self):
        """显式释放 CTCPrefixBeamSearch 类的资源。"""
        logger.info("CTCPrefixBeamSearch 显式释放资源")
        if hasattr(self, 'session'): 
            self.session = None

        # del self.session
        self.tokens.clear()
        self.times.clear()

    def ort_run(self, encoder_output: torch.Tensor) -> torch.Tensor:
        """使用 ONNX 会话运行 CTC 推理。
        Args:
            encoder_output (torch.Tensor): 编码器输出的张量,形状为 [batch_size, frame_num, hidden_dim]。
        Returns:
            torch.Tensor: CTC 输出的概率张量,形状为 [batch_size, frame_num, vocab_size]。
        """
        onnx_output = self.session.run(None, {'hidden': to_numpy(encoder_output)})
        ctc_probs = onnx_output[0]

        return torch.from_numpy(ctc_probs)

    def search(self, ctc_probs: torch.Tensor, ctc_lens: torch.Tensor) -> Tuple[List[int], List[float]]:
        """执行 CTC 前缀集束搜索。
        Args:
            ctc_probs (torch.Tensor): CTC 输出的概率张量,形状为 [batch_size, frame_num, vocab_size]。
            ctc_lens (torch.Tensor): 每个样本的长度信息,用于剪裁无效部分。
        Returns:
            Tuple[List[int], List[float]]: 最优路径的 token 序列和对应的时间戳序列。
        """
        
        new_ctc_probs = ctc_probs
        if len(self.ctc_beam_search_cache):
            new_ctc_probs = torch.cat((self.ctc_beam_search_cache[-1], ctc_probs), dim=1)   # 使用前一个chunk的输入缓存
        
        new_ctc_lens = torch.tensor([new_ctc_probs.size(1)])

        ctc_prefix_result = ctc_prefix_beam_search(
            new_ctc_probs, new_ctc_lens, beam_size=10, context_graph=self.context_graph
        )
        
        res = ctc_prefix_result[-1]
        best_tokens = list(res.tokens)
        best_times = list(res.times)

        if best_tokens:
            if len(self.ctc_beam_search_cache):
                cache_offset = self.ctc_beam_search_cache[-1].size(1)
            else:
                cache_offset = 0
            abs_times = [t - cache_offset + self.offset for t in best_times]
            cur_abs_times, cur_best_tokens = [], []
            # 去掉上一个chunk的结果
            for idx, t in enumerate(abs_times):
                if t not in self.last_chunk_times:
                    cur_abs_times.append(abs_times[idx])
                    cur_best_tokens.append(best_tokens[idx])
            self.last_chunk_times = cur_abs_times
            self.times.extend(cur_abs_times)
            self.tokens.extend(cur_best_tokens)
            self.offset += ctc_lens.item()   # 当前chunk右边界的绝对时点

        # 所有累积结果 根据时间点去重
        tuples = []
        if self.times:
            tuples = [(self.tokens[0], self.times[0])]
            for i in range(1, len(self.tokens)):
                token = self.tokens[i]
                time = self.times[i]
                if token == self.tokens[i-1] and time - self.times[i-1] <= 3:   # 如果前后token相同，且间隔小于等于3个时点（时间间隔0.12秒）可以认为是同一个字
                    pass
                elif (token, time) in tuples:
                    pass
                else:
                    tuples.append((token, time))

        self.tokens = [t[0] for t in tuples]
        self.times = [t[1] for t in tuples]
        logger.debug(f"ctc 前缀集束搜索完成")
        logger.debug(f"ctc token&time {tuples}")

        self.ctc_beam_search_cache = [ctc_probs]   # 保存上一个chunk的输入

        return self.tokens, self.times

class Decoder:
    def __init__(self, configs: dict):
        """Decoder 初始化类(保留接口,具体实现可扩展)。
        Args:
            configs (dict): 配置参数字典,包含模型相关的配置信息。
        """
        pass

    def ort_run(self) -> None:
        """运行解码器推理(待实现)。"""
        pass

class ASRDecoder:
    def __init__(self, args, configs: dict, symbol_table: SymbolTable, custom_separator: str = None):
        """ASR 解码器初始化类,负责整体解码流程。
        Args:
            args(dict): server_config_$l.yaml中的参数, 包含服务相关的配置信息
            configs (dict): train.yaml中的参数, 包含模型相关的配置信息。
            symbol_table: 词表, 用于将 token ID 转换为文本。
            custom_separator (str, optional): 用户自定义分隔符，会覆盖配置文件中的默认分隔符
        """
        self.configs = configs
        self.symbol_table = symbol_table

        # 初始化模型
        self.encoder = Encoder(self.configs)
        self.ctc = CTCPrefixBeamSearch(self.configs, self.symbol_table.char2id_dict)
        self.decoder = None
        if len(ONNX_SESSIONS) == 3:
            self.decoder = Decoder(self.configs)

        # 解码相关参数
        self.offset = 0
        self.chunk_index = 0
        self.result = ""

        self.decoding_window = self.configs["decoding_window"]
        self.right_context = self.configs["right_context"]
        self.subsampling = self.configs["subsampling_rate"]
        self.chunk_size = self.configs["chunk_size"]
        self.num_left_chunks = self.configs["left_chunks"]
        self.stride = self.subsampling * self.chunk_size
        self.required_cache_size = self.chunk_size * self.num_left_chunks

        # 解码间隔相关参数
        # 优先使用用户自定义分隔符，否则使用配置文件中的默认分隔符，最后使用空格作为兜底
        if custom_separator is not None:
            self.blank = custom_separator
        elif hasattr(args, 'default_separator') and args.default_separator:
            self.blank = args.default_separator
        else:
            self.blank = " "     # 兜底默认分隔符

        self.blank_id = 0
        self.blank_symbol = '<blank>'
        self.blank_interval = args.blank_interval if "blank_interval" in args else 0.5    # 单位:秒, 时点超过该间隔阈值则添加间隔符
        frame_shift = self.configs["feat_configs"]["frame_shift"]
        self.time_scale = self.subsampling * (frame_shift / 1000.0)   # 将毫秒转化为秒

    def __del__(self):
        """显式释放 ASRDecoder 类的资源。"""
        logger.info("ASRDecoder 显式释放资源")
        if hasattr(self, 'encoder'): 
            self.encoder = None
        if hasattr(self, 'ctc'): 
            self.ctc = None
        if hasattr(self, 'decoder'): 
            self.decoder = None
        if hasattr(self, 'result'): 
            self.result = None

        # del self.encoder
        # del self.ctc
        # del self.decoder
        # del self.result

    def decode_chunk(self, chunk: torch.Tensor, chunk_index: int, client_id: str, is_final: bool) -> str:
        """逐块解码函数。
        Args:
            chunk (torch.Tensor): 当前处理的语音特征张量,形状为 [batch_size, chunk_size, feature_dim]。
            chunk_index (int): 当前处理的 chunk 索引。
            client_id (str): 客户端 ID,用于日志区分。
            is_final (bool): 是否是最后一个 chunk(决定是否执行最终结果处理)。
        Returns:
            str: 解码得到的结果文本。
        """
        chunk_len = chunk.size(1)
        if chunk_len < self.right_context:
            logger.debug(f"chunk before pad: {chunk.shape}")
            pad_len = math.ceil((self.right_context - chunk_len)/2)
            chunk = F.pad(chunk, (0, 0, pad_len, pad_len))
            logger.debug(f"chunk after pad: {chunk.shape}")
        # 1.编码器推理
        encoder_output = self.encoder.ort_run(chunk, chunk_index)
            
        # 2.CTC 前缀集束搜索 
        ctc_start_offset = self.ctc.offset     # 避免每次 beam_search 从头搜索
        encoder_output = encoder_output[:, ctc_start_offset:, :]
        encoder_lens = torch.tensor([encoder_output.shape[1]])
        
        logger.debug(f"client_id:{client_id} - encoder 累计时点: {self.encoder.offset}")
        logger.debug(f"client_id:{client_id} - ctc 搜索起始时点: {ctc_start_offset} encoder_lens: {encoder_lens}")
        
        ctc_probs = self.ctc.ort_run(encoder_output)
        best_tokens, best_times = self.ctc.search(ctc_probs, encoder_lens)   # 从头累计的所有 tokens

        # 3.解码器注意力重排序
        if self.decoder:
            result = self.decoder.ort_run()  # 待实现

        # 4.根据时间间隔添加空格 并且 detokenize
        if is_final:
            result = self.detokenize(best_tokens, best_times)
        else:
            result = self.detokenize(best_tokens, [])
        logger.debug(f"client_id:{client_id} - ctc 累计搜索结果: \"{result}\"")

        return result

    def decode(self, accum_feats: torch.Tensor, client_id: str, is_final: bool = False) -> None:
        """整体解码函数,负责处理累积的特征并逐步解码。
        Args:
            accum_feats (torch.Tensor): 累积的语音特征张量,形状为 [batch_size=1, num_frames, num_mel_bins]。
            client_id (str): 客户端 ID,用于日志区分。
            is_final (bool, optional): 是否是最终解码(决定是否处理剩余部分)。默认值为 False。
        """
        accm_num_frames = accum_feats.shape[1]

        if self.offset <= accm_num_frames:
            # 逐 chunk 解码
            start_decode_offset = self.offset
            for cur in range(start_decode_offset, accm_num_frames - self.decoding_window + 1, self.stride):
                end = min(cur + self.decoding_window, accm_num_frames)
                chunk_feats = accum_feats[:, cur:end, :]
                
                logger.debug(
                    f"client_id:{client_id} - 第{self.chunk_index}个chunk, 原始帧: {cur}~{end},  {chunk_feats.shape}"
                )
                result = self.decode_chunk(chunk_feats, self.chunk_index, client_id, is_final)
                
                self.result = result
                self.chunk_index += 1 
                self.offset += self.stride
                
        if is_final and self.offset < accm_num_frames:
            chunk_feats = accum_feats[:, self.offset:, :]
            logger.debug(
                f"client_id:{client_id} - 第{self.chunk_index}个chunk, 原始帧: {self.offset}~{accm_num_frames},  {chunk_feats.shape}"
            )
            
            result = self.decode_chunk(chunk_feats, self.chunk_index, client_id, is_final)
            self.result = result.replace("<blank>", " ")
            self.chunk_index += 1 
            self.offset += self.stride

    def detokenize(self, tokens: List[int], times: List[float]) -> str:
        """根据时间戳间隔添加空格,并将 token 转换为文本。
        Args:
            tokens (List[int]): 解码得到的 token 序列。
            times (List[float]): 对应的时戳序列(单位：帧)。
        Returns:
            str: 处理后的最终文本结果。
        """
        if len(times):
            res = []
            times_sec = [x * self.time_scale for x in times]
            for i in range(len(times_sec) - 1):
                res.append(tokens[i])
                if times_sec[i+1] - times_sec[i] > self.blank_interval:  # token 时间戳间隔大于0.35秒,加一个空格, 阈值可以通过参数指定
                    res.append(self.blank_id)
            res.append(tokens[-1])
            
            chars = self.symbol_table.ids2tokens(res)
            text = "".join(chars).replace(self.blank_symbol, self.blank)
        else:
            chars = self.symbol_table.ids2tokens(tokens)
            text = "".join(chars)
        
        text = self.symbol_table.char_map(text)

        return text
