# ASR服务全局配置文件
# 通用配置参数，语种特定的参数仍在各自的config_$lang.yaml中

# 服务器配置
server:
  host: "0.0.0.0"
  port: 10080
  heartbeat_interval: 30  # 心跳间隔（秒）
  max_connections: 100    # 最大并发连接数
  connection_timeout: 300 # 连接超时时间（秒）

# 音频处理配置
audio:
  valid_sample_rate_list: [44100, 16000, 8000]
  expected_sample_rate: 16000
  expected_sample_width: 2
  expected_sample_channels: 1
  expected_data_size: 12800
  expected_time_interval: 6
  # 音频缓存管理
  max_audio_buffer_frames: 120  # 最大缓存帧数（约60秒@16kHz）
  buffer_cleanup_interval: 60   # 缓存清理间隔（秒）

# LID（语种识别）配置
lid:
  enabled: true
  model_path: "/ws/MODELS/lid_model"
  # LID检测参数
  min_duration: 0.4      # 最小检测时长（秒）
  max_duration: 2.4      # 最大检测时长（秒）
  detection_interval: 0.4 # 检测间隔（秒）
  max_attempts: 6        # 最大尝试次数
  confidence_threshold: 0.7  # 置信度阈值
  # 静音检测参数
  silence_threshold: 10  # 静音阈值（帧数）
  vad_enabled: true      # 是否启用VAD

# ONNX会话池配置
onnx_session_pool:
  enabled: true
  max_sessions_per_model: 4  # 每个模型的最大会话数
  session_timeout: 300       # 会话超时时间（秒）
  preload_all_languages: true # 是否预加载所有语种模型

# 性能监控配置
monitoring:
  memory_check_interval: 60    # 内存检查间隔（秒）
  memory_warning_threshold: 80 # 内存警告阈值（百分比）
  memory_critical_threshold: 90 # 内存严重阈值（百分比）
  enable_health_check: true    # 是否启用健康检查
  health_check_port: 8081     # 健康检查端口

# 日志配置
logging:
  level: "INFO"
  rotation: "1 day"
  retention: "7 days"
  compression: "zip"
  max_file_size: "100 MB"

# 错误处理配置
error_handling:
  max_retries: 3
  retry_delay: 1.0  # 重试延迟（秒）
  enable_detailed_errors: true

# 支持的语种列表
supported_languages:
  - zh
  - en
  - ru
  - kk
  - kkin
  - ug

# 多语种模式配置
multi_language:
  default_language: "zh"  # 默认语种
  fallback_language: "zh" # 回退语种
  enable_auto_switch: true # 是否启用自动切换
