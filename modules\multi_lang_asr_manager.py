#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ASR模型加载器
统一管理单语种和多语种的ASR模型加载，支持动态切换
"""

import os
import threading
from typing import Dict, Optional, Any, List
from modules.logger import logger
from modules.decoder import load_onnx, ONNX_SESSIONS
from modules.symbol_table import SymbolTable
from modules.config import config_manager
from modules.onnx_session_pool import get_global_session_pool


class ASRModelLoader:
    """统一的ASR模型加载器"""

    def __init__(self, global_config):
        """
        初始化ASR模型加载器

        Args:
            global_config: 全局配置对象
        """
        self.global_config = global_config
        self.supported_languages = global_config.supported_languages
        self.loaded_models = {}  # 存储已加载的模型信息 {lang_code: {sessions, metadatas, config, symbol_table}}

        # 线程安全锁
        self.model_lock = threading.RLock()
        self.session_pool = get_global_session_pool()

        logger.info("初始化ASR模型加载器")

    def load_models(self, lang_codes) -> bool:
        """
        根据lang_code加载模型
        Args:
            lang_codes: 语种代码，可以是字符串或列表
                       - 'multi': 加载所有支持的语种
                       - 'zh': 加载单个语种
                       - ['zh', 'en']: 加载指定的多个语种
        Returns:
            bool: 是否加载成功
        """
        # 处理lang_codes参数
        if isinstance(lang_codes, str):
            if lang_codes == "multi":
                target_languages = self.supported_languages
                logger.info("多语种模式：预加载所有支持的语种模型")
            else:
                target_languages = [lang_codes]
                logger.info(f"单语种模式：加载语种 {lang_codes}")
        else:
            target_languages = lang_codes
            logger.info(f"指定语种模式：加载语种 {target_languages}")

        # 加载每个语种的模型
        success_count = 0
        for lang_code in target_languages:
            if self._load_single_language(lang_code):
                success_count += 1
            else:
                logger.warning(f"加载语种 {lang_code} 失败")

        logger.info(f"模型加载完成：成功 {success_count}/{len(target_languages)} 个语种")
        return success_count > 0

    def _load_single_language(self, lang_code: str) -> bool:
        """
        加载指定语种的ASR模型

        Args:
            lang_code: 语种代码

        Returns:
            bool: 是否加载成功
        """
        with self.model_lock:
            if lang_code in self.loaded_models:
                logger.info(f"语种 {lang_code} 的模型已加载")
                return True

            try:
                # 获取语种配置
                lang_config = config_manager.load_language_config(lang_code)
                if not lang_config:
                    logger.error(f"无法加载语种 {lang_code} 的配置")
                    return False

                # 验证模型路径
                if not os.path.exists(lang_config.onnx_dir):
                    logger.error(f"语种 {lang_code} 的模型路径不存在: {lang_config.onnx_dir}")
                    return False

                # 保存当前全局ONNX_SESSIONS
                original_sessions = ONNX_SESSIONS.copy()
                ONNX_SESSIONS.clear()

                # 加载语种特定的模型
                metadatas = load_onnx(
                    lang_config.onnx_dir,
                    lang_config.fp16,
                    lang_config.quant,
                    self.global_config.decode.mode,
                    lang_config.device,
                    lang_config.device_id
                )

                # 使用新的配置构建方法
                model_config = lang_config.build_model_config(metadatas, self.global_config.decode)

                # 创建词表
                symbol_table = SymbolTable(
                    lang_config.onnx_dir,
                    lang_config.dict_path,
                    lang_code
                )

                # 保存加载的模型信息
                self.loaded_models[lang_code] = {
                    'sessions': ONNX_SESSIONS.copy(),
                    'metadatas': metadatas,
                    'config': model_config,
                    'symbol_table': symbol_table,
                    'lang_config': lang_config
                }

                # 恢复原始ONNX_SESSIONS
                ONNX_SESSIONS.clear()
                ONNX_SESSIONS.update(original_sessions)

                logger.info(f"成功加载语种 {lang_code} 的ASR模型")
                return True

            except Exception as e:
                logger.error(f"加载语种 {lang_code} 的ASR模型失败: {e}")
                return False
    


    def switch_to_language(self, lang_code: str) -> bool:
        """
        切换到指定语种的模型

        Args:
            lang_code: 目标语种代码

        Returns:
            bool: 是否切换成功
        """
        if lang_code not in self.supported_languages:
            logger.warning(f"不支持的语种: {lang_code}")
            return False

        # 如果模型未加载，先加载
        if lang_code not in self.loaded_models:
            if not self._load_single_language(lang_code):
                return False

        try:
            # 切换全局ONNX_SESSIONS到目标语种
            ONNX_SESSIONS.clear()
            ONNX_SESSIONS.update(self.loaded_models[lang_code]['sessions'])

            logger.info(f"成功切换到语种: {lang_code}")
            return True

        except Exception as e:
            logger.error(f"切换到语种 {lang_code} 失败: {e}")
            return False

    def get_symbol_table(self, lang_code: str) -> Optional[SymbolTable]:
        """获取指定语种的词表"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['symbol_table']
        return None

    def get_config(self, lang_code: str) -> Optional[Dict]:
        """获取指定语种的配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['config']
        return None

    def get_lang_config(self, lang_code: str) -> Optional[Any]:
        """获取指定语种的语言配置"""
        if lang_code in self.loaded_models:
            return self.loaded_models[lang_code]['lang_config']
        return None
    
    def preload_common_languages(self, languages: List[str] = None):
        """
        预加载常用语种的模型

        Args:
            languages: 要预加载的语种列表，默认为所有支持的语种
        """
        if languages is None:
            # 默认预加载所有支持的语种以确保实时切换
            languages = self.supported_languages.copy()

        logger.info(f"开始预加载语种: {languages}")
        return self.load_models(languages)
    
    def is_language_loaded(self, lang_code: str) -> bool:
        """检查指定语种是否已加载"""
        return lang_code in self.loaded_models
    
    def get_loaded_languages(self) -> List[str]:
        """获取已加载的语种列表"""
        return list(self.loaded_models.keys())

    def unload_language(self, lang_code: str):
        """卸载指定语种的模型"""
        if lang_code in self.loaded_models:
            del self.loaded_models[lang_code]
            logger.info(f"已卸载语种 {lang_code} 的模型")

    def cleanup(self):
        """清理所有资源"""
        logger.info("清理ASR模型加载器资源")
        self.loaded_models.clear()
        ONNX_SESSIONS.clear()


# 为了保持向后兼容性，创建别名
MultiLangASRManager = ASRModelLoader
