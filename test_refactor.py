#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
重构验证测试脚本
验证新架构的语法正确性
"""

import sys
import os
import ast

def print_log(msg):
    """简单的日志输出函数"""
    print(f"[TEST] {msg}")

def test_syntax(file_path):
    """测试Python文件的语法正确性"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()

        # 编译检查语法
        ast.parse(source, filename=file_path)
        return True
    except SyntaxError as e:
        print_log(f"语法错误 {file_path}:{e.lineno}: {e.msg}")
        return False
    except Exception as e:
        print_log(f"文件读取错误 {file_path}: {e}")
        return False


def test_syntax_check():
    """测试重构文件的语法正确性"""
    print_log("=== 测试语法正确性 ===")

    # 需要检查的文件列表
    files_to_check = [
        'modules/config.py',
        'modules/multi_lang_asr_manager.py',
        'server.py',
        'modules/connect.py'
    ]

    passed = 0
    total = len(files_to_check)

    for file_path in files_to_check:
        if os.path.exists(file_path):
            if test_syntax(file_path):
                print_log(f"✓ {file_path} 语法正确")
                passed += 1
            else:
                print_log(f"✗ {file_path} 语法错误")
        else:
            print_log(f"✗ {file_path} 文件不存在")

    return passed == total


def test_import_check():
    """测试模块导入"""
    print_log("=== 测试模块导入 ===")

    # 测试关键模块的导入
    modules_to_test = [
        ('modules.config', ['ConfigManager', 'parse_args']),
        ('modules.multi_lang_asr_manager', ['ASRModelLoader']),
    ]

    passed = 0
    total = len(modules_to_test)

    for module_name, symbols in modules_to_test:
        try:
            # 动态导入模块
            module = __import__(module_name, fromlist=symbols)

            # 检查符号是否存在
            missing_symbols = []
            for symbol in symbols:
                if not hasattr(module, symbol):
                    missing_symbols.append(symbol)

            if missing_symbols:
                print_log(f"✗ {module_name} 缺少符号: {missing_symbols}")
            else:
                print_log(f"✓ {module_name} 导入成功，符号完整")
                passed += 1

        except Exception as e:
            print_log(f"✗ {module_name} 导入失败: {e}")

    return passed == total


def main():
    """主测试函数"""
    print_log("开始重构验证测试（语法检查模式）")

    tests = [
        ("语法检查", test_syntax_check),
        ("导入检查", test_import_check),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print_log(f"\n开始测试: {test_name}")
        try:
            if test_func():
                print_log(f"✓ {test_name} 测试通过")
                passed += 1
            else:
                print_log(f"✗ {test_name} 测试失败")
        except Exception as e:
            print_log(f"✗ {test_name} 测试异常: {e}")

    print_log(f"\n测试结果: {passed}/{total} 通过")

    if passed == total:
        print_log("🎉 所有测试通过！重构语法验证成功")
        return True
    else:
        print_log("⚠️  部分测试失败，请检查相关问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
