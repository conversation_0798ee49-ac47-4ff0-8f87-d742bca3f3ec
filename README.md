# ASR服务器 - 实时语音识别服务

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.68+-green.svg)](https://fastapi.tiangolo.com)
[![WebSocket](https://img.shields.io/badge/WebSocket-Supported-orange.svg)](https://websockets.readthedocs.io)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🚀 项目简介

ASR服务器是一个高性能的实时语音识别服务，基于WebSocket协议提供流式音频处理能力。支持多语种识别、语言自动检测、并发处理等企业级功能。

### ✨ 主要特性

- **🎯 实时识别**: 基于WebSocket的流式音频处理
- **🌍 多语种支持**: 支持中文、英文、俄语、哈萨克语、维吾尔语等
- **🤖 智能检测**: 自动语言识别(LID)和模型切换
- **⚡ 高性能**: ONNX会话池支持高并发处理
- **📊 系统监控**: 实时监控内存、CPU、连接状态
- **🔧 易于部署**: 支持Docker、systemd等多种部署方式
- **🛡️ 生产就绪**: 完整的错误处理、日志记录、健康检查

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Python**: 3.8+
- **内存**: 最小4GB，推荐8GB+
- **存储**: 至少10GB (用于模型文件)
- **网络**: 稳定的网络连接

## 🛠️ 快速开始

### 方法一：自动安装脚本 (推荐)

```bash
# 克隆项目
git clone <repository_url>
cd ASRServer/03_Individual_stream_asr_server

# 运行自动安装脚本
./install.sh
```

### 方法二：手动安装

```bash
# 1. 安装系统依赖
sudo apt-get update
sudo apt-get install -y gcc g++ make libsndfile1-dev python3-dev python3-venv

# 2. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 3. 安装Python依赖
pip install -r requirements.txt

# 4. 可选：安装psutil以获得更好的监控
pip install psutil

# 5. 创建必要目录
mkdir -p logs models backup
```

### 启动服务

```bash
# 多语种模式
python server.py --multi-lang --config config.yaml

# 单语种模式
python server.py --lang zh --config config_zh.yaml
```

## 📖 使用文档

- **[用户手册](USER_MANUAL.md)** - 完整的使用指南和API文档
- **[优化总结](OPTIMIZATION_SUMMARY.md)** - 项目优化详情和技术细节

## 🔧 配置说明

### 主配置文件 (config.yaml)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  heartbeat_interval: 30
  max_connections: 100

# ONNX会话池配置
onnx_session_pool:
  enabled: true
  max_sessions_per_model: 4
  session_timeout: 300

# 系统监控配置
monitoring:
  enable_health_check: true
  memory_warning_threshold: 80
  health_check_port: 8081
```

## 🌐 API接口

### WebSocket连接
```
ws://localhost:8000/ws/{client_id}
```

### REST API
- `GET /api/transmission-rules` - 获取数据传输规则
- `GET /health` - 健康检查
- `GET /metrics` - 系统指标 (端口8081)

### 消息格式

**音频数据包**:
```json
{
  "type": "audio",
  "client_id": "your-client-id",
  "index": 1,
  "data": "base64_encoded_audio_data",
  "sample_rate": 16000,
  "language": "zh"
}
```

**识别结果**:
```json
{
  "type": "result",
  "client_id": "your-client-id",
  "text": "识别结果文本",
  "confidence": 0.95,
  "language": "zh",
  "is_final": true
}
```

## 📊 监控和健康检查

### 监控端点
- **主服务**: `http://localhost:8000/health`
- **详细监控**: `http://localhost:8081/metrics`
- **历史数据**: `http://localhost:8081/metrics/history`
- **统计信息**: `http://localhost:8081/stats`

### 监控指标
- 内存使用率
- CPU使用率
- 磁盘使用率
- 活跃连接数
- 请求总数
- 错误计数

## 🐳 Docker部署

```bash
# 构建镜像
docker build -t asr-server .

# 运行容器
docker run -d \
  --name asr-server \
  -p 8000:8000 \
  -p 8081:8081 \
  -v ./models:/app/models:ro \
  -v ./logs:/app/logs \
  asr-server
```

或使用docker-compose:
```bash
docker-compose up -d
```

## 🔒 安全配置

### 防火墙设置
```bash
# 允许服务端口
sudo ufw allow 8000/tcp
sudo ufw allow 8081/tcp

# 限制监控端口访问
sudo ufw allow from ***********/16 to any port 8081
```

### SSL/TLS支持
支持通过nginx反向代理配置HTTPS和WSS。

## 📈 性能优化

### 系统级优化
- 调整文件描述符限制: `ulimit -n 65536`
- 优化TCP参数
- 使用SSD存储模型文件

### 应用级优化
- 调整ONNX会话池大小
- 优化音频缓存参数
- 合理设置并发连接数

## 🔍 故障排查

### 常见问题
1. **服务启动失败**: 检查端口占用和权限
2. **模型加载失败**: 检查模型文件路径和完整性
3. **内存使用过高**: 调整缓存参数或增加内存
4. **连接断开**: 检查网络稳定性和心跳设置

### 日志查看
```bash
# 查看服务日志
tail -f logs/asr_server.log

# 查看系统服务日志
sudo journalctl -u asr-server -f
```

## 🧪 测试

### 单元测试
```bash
python -m pytest tests/
```

### 性能测试
```bash
# 并发连接测试
python tests/stress_test.py --connections 50 --duration 300
```

## 📦 项目结构

```
ASRServer/03_Individual_stream_asr_server/
├── server.py                 # 主服务器文件
├── config.yaml              # 主配置文件
├── config_*.yaml            # 语种特定配置
├── requirements.txt         # Python依赖
├── install.sh              # 自动安装脚本
├── USER_MANUAL.md          # 用户手册
├── OPTIMIZATION_SUMMARY.md # 优化总结
├── modules/                # 核心模块
│   ├── connect.py          # 连接管理
│   ├── multi_lang_asr_manager.py  # 多语种管理
│   ├── onnx_session_pool.py       # 会话池
│   ├── monitoring.py              # 系统监控
│   ├── error_codes.py            # 错误码系统
│   └── ...
├── models/                 # 模型文件目录
├── logs/                  # 日志目录
└── backup/               # 备份目录
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

- **文档**: [用户手册](USER_MANUAL.md)
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **邮件支持**: [<EMAIL>](mailto:<EMAIL>)

## 🏆 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**版本**: v1.0  
**最后更新**: 2024-01-01  
**维护者**: ASR Team
