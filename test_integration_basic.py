#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
LID集成基本测试脚本
测试代码结构和导入，不依赖外部模型
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    try:
        # 测试基础模块导入
        from modules.logger import logger
        print("✅ logger导入成功")
        
        from modules.config import parse_args
        print("✅ config导入成功")
        
        # 测试ConnectionManager导入和LID方法
        from modules.connect import ConnectionManager
        print("✅ ConnectionManager导入成功")
        
        # 检查LID相关方法
        methods = dir(ConnectionManager)
        lid_methods = [m for m in methods if 'lid' in m.lower() or '_process_lid' in m or '_perform_lid' in m]
        print(f"✅ LID相关方法: {lid_methods}")
        
        if '_process_lid' not in methods or '_perform_lid' not in methods:
            print("❌ 缺少LID处理方法")
            return False
        
        # 测试server导入
        import server
        print("✅ server.py导入成功")
        
        # 检查LID_MANAGER变量
        if hasattr(server, 'LID_MANAGER'):
            print("✅ 找到LID_MANAGER全局变量")
        else:
            print("❌ 缺少LID_MANAGER全局变量")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_config():
    """测试配置功能"""
    print("\n" + "=" * 50)
    print("测试配置功能")
    print("=" * 50)
    
    try:
        from modules.config import parse_args
        
        # 保存原始命令行参数
        original_argv = sys.argv
        
        try:
            # 模拟命令行参数
            sys.argv = ['test_script', 'zh', '8080']
            args = parse_args()
            
            print(f"✅ 配置解析成功")
            print(f"✅ 语言代码: {args.lang_code}")
            print(f"✅ 端口: {args.port}")
            print(f"✅ LID模型路径: {getattr(args, 'lid_model_path', 'NOT_CONFIGURED')}")
            
            if not hasattr(args, 'lid_model_path'):
                print("❌ 缺少lid_model_path配置")
                return False
            
            return True
            
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_connection_manager_structure():
    """测试ConnectionManager结构"""
    print("\n" + "=" * 50)
    print("测试ConnectionManager结构")
    print("=" * 50)
    
    try:
        from modules.connect import ConnectionManager
        
        # 检查构造函数签名
        import inspect
        sig = inspect.signature(ConnectionManager.__init__)
        params = list(sig.parameters.keys())
        print(f"✅ 构造函数参数: {params}")
        
        if 'lid_manager' not in params:
            print("❌ 构造函数缺少lid_manager参数")
            return False
        
        # 检查关键方法存在
        required_methods = ['connect', 'disconnect', 'on_check', 'on_decode', 'on_result', '_process_lid', '_perform_lid']
        methods = dir(ConnectionManager)
        
        missing_methods = [m for m in required_methods if m not in methods]
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        
        print("✅ 所有必需方法都存在")
        return True
        
    except Exception as e:
        print(f"❌ ConnectionManager结构测试失败: {e}")
        return False

def test_config_files():
    """测试配置文件"""
    print("\n" + "=" * 50)
    print("测试配置文件")
    print("=" * 50)
    
    config_files = ['server_config_zh.yaml', 'server_config_en.yaml']
    
    for config_file in config_files:
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'lid_model_path' in content:
                    print(f"✅ {config_file}: 包含lid_model_path配置")
                else:
                    print(f"❌ {config_file}: 缺少lid_model_path配置")
                    return False
            else:
                print(f"⚠️  {config_file}: 文件不存在")
                
        except Exception as e:
            print(f"❌ {config_file}测试失败: {e}")
            return False
    
    return True

def test_requirements():
    """测试依赖文件"""
    print("\n" + "=" * 50)
    print("测试依赖文件")
    print("=" * 50)
    
    try:
        if os.path.exists('requirements.txt'):
            with open('requirements.txt', 'r') as f:
                content = f.read()
            
            required_deps = ['torch', 'torchaudio', 'webrtcvad']
            missing_deps = []
            
            for dep in required_deps:
                if dep in content:
                    print(f"✅ 找到依赖: {dep}")
                else:
                    missing_deps.append(dep)
            
            if missing_deps:
                print(f"❌ 缺少依赖: {missing_deps}")
                return False
            
            return True
        else:
            print("❌ requirements.txt文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 依赖测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始LID集成基本测试...")
    print("注意：这是基本的结构测试，不涉及实际模型加载")
    
    tests = [
        ("模块导入", test_imports),
        ("配置功能", test_config),
        ("ConnectionManager结构", test_connection_manager_structure),
        ("配置文件", test_config_files),
        ("依赖文件", test_requirements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"\n✅ {test_name}: 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name}: 失败")
        except Exception as e:
            print(f"\n❌ {test_name}: 异常 - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 50)
    
    if passed == total:
        print("🎉 所有基本集成测试通过！")
        print("\n集成完成情况:")
        print("✅ LID管理器模块已创建")
        print("✅ ConnectionManager已集成LID功能")
        print("✅ 服务器配置已支持LID")
        print("✅ WebSocket接口已扩展")
        print("✅ 依赖文件已更新")
        
        print("\n下一步操作:")
        print("1. 安装新增的依赖: pip install torch torchaudio")
        print("2. 准备LID模型文件")
        print("3. 在配置文件中设置正确的LID模型路径")
        print("4. 启动服务器: python server.py zh 8080")
        print("5. 使用客户端测试多语种音频识别")
        
        return True
    else:
        print("⚠️  部分测试失败，请检查集成代码")
        return False

if __name__ == "__main__":
    main()
